<nz-table
  #basicTable
  [nzBordered]="borderedGrid"
  [nzData]="displayRows"
  [nzSize]="'default'"
  [nzScroll]="nzScroll"
  [nzLoading]="false"
  [nzPaginationPosition]="paginationPosition"
  [nzShowPagination]="!loading && showPagination"
  [nzFrontPagination]="!loading && !backendPagination && frontPagination"
  [nzShowTotal]="showTotal ? rangeTemplate : null"
  [nzPageSize]="pageSize"
  [nzPageIndex]="pageIndex"
  [nzTotal]="total || filteredData.length"
  [nzShowSizeChanger]="showSizeChanger"
  [nzShowQuickJumper]="showQuickJumper"
  [nzPageSizeOptions]="pageSizeOptions"
  (nzPageSizeChange)="onPageSizeChange($event)"
  (nzPageIndexChange)="onPageIndexChange($event)"
  (nzQueryParams)="onQuerryParamsChange($event)"
  [class.left-aligned-pagination]="paginationHorizontalPosition === 'left'"
>
  <thead>
    <tr class="table-header-row">
      @for (column of updatedTableColumn; track column.field) {
        <th
          [style.background-color]="headerBgColor"
          [style.color]="color"
          [style.text-align]="headerTextAlign"
          [style.border]="headerBorder ? '1px solid #afafaf' : 'none'"
          [nzShowSort]="false"
          [nzSortFn]="!backendSort ? column.sortFn || null : null"
          [nzSortOrder]="column.sortOrder ?? null"
          [nzWidth]="column.width ?? null"
          
          [nzShowFilter]="!loading && !!(column.filterFn || null)"
          [nzFilters]="(!loading && column.listOfFilter) || []"
          (nzFilterChange)="onFilterChange($event, column)"
          [nzFilterFn]="!loading && (column.filterFn || null)"
        >
          <!-- <div class="d-flex justify-content-between"> -->
          {{ column.header }}
          @if (!loading && (column.sortFn !== undefined || backendSort)) {
            <span class="sort-icons">
              <i
                nz-icon
                nzType="caret-up"
                [class.active]="column.sortOrder === enumSortType.ascend"
                nzTheme="fill"
                (click)="sortData(column, enumSortType.ascend)"
              ></i>
              <i
                nz-icon
                nzType="caret-down"
                [class.active]="column.sortOrder === enumSortType.descend"
                nzTheme="fill"
                (click)="sortData(column, enumSortType.descend)"
              ></i>
            </span>
          }
          <!-- </div> -->
        </th>
      }
    </tr>
  </thead>
  <tbody
    cdkDropList
    [cdkDropListDisabled]="!dragEnabled"
    (cdkDropListDropped)="drop($event)"
  >
    @for (
      rowData of basicTable.data;
      track rowData[trackById] || rowIndex;
      let rowIndex = $index
    ) {
      <tr
        [class]="getRowClassName(rowIndex)"
        [class.darker-Row]="evenRowdarker"
        [class.stripped]="stripAtLastColumn"
        [class.row-border]="rowBorder"
        (click)="onRowClick($event, rowData, rowIndex, getRowNumber(rowIndex))"
        cdkDrag
        [cdkDragDisabled]="!dragEnabled"
        [class.cursor-move]="dragEnabled"
        class="highlightRow table-body-row"
        [class.rounded-corner]="roundedCorners"
        [class.pointer]="rowPointer"
      >
        @for (col of updatedTableColumn; track col.field + $index) {
          <td
            [nzRight]="columnFreezed && !!col.isFrozenRight"
            [nzLeft]="columnFreezed && !!col.isFrozenLeft"
          >
            @if (
              loading && !['button', 'actionFields'].includes(col.fieldType)
            ) {
              <div
                [style.width]="col.width || '100%'"
                class="d-flex justify-content-center rounded-2"
              >
                <nz-skeleton
                  [nzTitle]="false"
                  [nzParagraph]="{ rows: 1, width: '90%' }"
                  [nzActive]="true"
                ></nz-skeleton>
              </div>
            }
            @switch (col.fieldType) {
              @case ('button') {
                @if (loading) {
                  <nz-skeleton-element
                    nzType="button"
                    [nzActive]="loading"
                  ></nz-skeleton-element>
                } @else {
                  <div
                    nz-tooltip
                    [nzTooltipTitle]="
                      !isBtnDisabled(rowData, col)
                        ? col.tooltipText
                        : col.getToolTipTextFn?.(rowData)
                    "
                    class="d-flex justify-content-center gap-4"
                  >
                    <button
                      nz-button
                      nzType="primary"
                      [nzGhost]="
                        col.secondaryBtnFn?.(rowData) ?? col.secondaryBtn
                      "
                      (click)="
                        onRowDataClick(
                          $event,
                          rowData,
                          col.field,
                          rowIndex,
                          getRowNumber(rowIndex)
                        )
                      "
                      [disabled]="isBtnDisabled(rowData, col)"
                      [ngClass]="getClassName(rowData, col)"
                    >
                      <i
                        *ngIf="
                          col.getBtnIconClassFn?.(rowData) ||
                          col.buttonIconClass
                        "
                        [ngClass]="
                          col.getBtnIconClassFn?.(rowData) ||
                          col.buttonIconClass
                        "
                        class="me-2"
                      ></i>
                      {{
                        col.btnLabelFn?.(rowData) ??
                          col?.btnLabelText ??
                          col.field
                      }}
                    </button>
                  </div>
                }
              }

              @case ('icon') {
                @if (loading) {
                  <nz-skeleton-element
                    nzType="button"
                    [nzSize]="'small'"
                    [nzActive]="loading"
                    [nzShape]="'square'"
                  ></nz-skeleton-element>
                } @else {
                  <span class="d-flex justify-content-center pointer">
                    <button nz-button nzType="text">
                      <i
                        [ngClass]="col.iconClass || col.iconClasses"
                        nz-tooltip
                        [nzTooltipTitle]="col.iconTooltipTitle"
                        [nzTooltipPlacement]="col?.tooltipPlacement"
                        (click)="
                          onRowDataClick(
                            $event,
                            rowData,
                            col.field,
                            rowIndex,
                            getRowNumber(rowIndex)
                          )
                        "
                      ></i>
                    </button>
                  </span>
                }
              }

              @case ('link') {
                <span class="d-flex justify-content-center position-relative">
                  <a
                    class="link"
                    (click)="
                      onRowDataClick(
                        $event,
                        rowData,
                        col.field,
                        rowIndex,
                        getRowNumber(rowIndex)
                      )
                    "
                  >
                    <span [innerHTML]="highlight(rowData[col.field])"></span
                  ></a>
                </span>
              }

              @case ('textArea') {
                @if (col.isEditable) {
                  <textarea
                    [(ngModel)]="rowData[col.field]"
                    class="form-control"
                    rows="2"
                    (ngModelChange)="onFieldDataChange(rowData, col, $event)"
                    [disabled]="!!col.isDisabled"
                  ></textarea>
                } @else {
                  <div
                    [class.text-area]="!rowData?.['isExpandedTextArea']"
                    [class.expanded-text]="rowData?.['isExpandedTextArea']"
                    class="pointer text-area-field"
                    [style.max-width]="col.width"
                    [title]="rowData[col.field]"
                    [innerHTML]="highlight(rowData[col.field])"
                  ></div>
                }
              }

              @case ('image') {
                <img
                  [src]="rowData[col.field]"
                  alt="No image"
                  style="width: 30px; height: 30px"
                  (click)="
                    onRowDataClick(
                      $event,
                      rowData,
                      col.field,
                      rowIndex,
                      getRowNumber(rowIndex)
                    )
                  "
                />
              }

              @case ('profile') {
                <div class="d-flex align-items-center gap-2">
                  <img
                    *ngIf="col.profileImageField"
                    [src]="
                      rowData[col.profileImageField] ||
                      'assets/profileImage.png'
                    "
                    alt="img"
                    class="profile-image me-2"
                  />
                  <span class="fullname text-style">{{
                    rowData[col.fullNameField!]
                  }}</span>
                </div>
              }

              @case ('dateField') {
                <span
                  [ngClass]="getClassName(rowData, col)"
                  class="text-style"
                  >{{ formatDateField(rowData[col.field]) }}</span
                >
              }

              @case ('dateTime') {
                <span
                  [ngClass]="getClassName(rowData, col)"
                  class="text-style"
                  >{{
                    formatDateField(rowData[col.field], { displayTime: true })
                  }}</span
                >
              }

              @case ('dateInput') {
                @if (col.isEditable) {
                  <!-- <app-date-picker [dateField]="rowData[col.field]" (changeDate)="onFieldDataChange(rowData,col,$event)"/> -->
                } @else {
                  <span
                    [ngClass]="getClassName(rowData, col)"
                    class="text-style"
                    >{{ formatDateField(rowData[col.field]) }}</span
                  >
                }
              }

              @case ('text') {
                @if (!loading) {
                  <span
                    class="text-style"
                    [ngClass]="getClassName(rowData, col)"
                    [innerHTML]="
                      highlight(
                        col.conditionForAltText?.(rowData)
                          ? col.getAltText?.(rowData) || col.altText
                          : rowData[col.field]
                      )
                    "
                  ></span>
                  @if (col?.conditionForShowIcon?.(rowData)) {
                    <i
                      class="icon-position"
                      nz-tooltip
                      [ngClass]="col?.iconClasses"
                      [nzTooltipPlacement]="col?.tooltipPlacement"
                      [nzTooltipTitle]="
                        col.tooltipText || col?.getIconToolTipTextFn?.(rowData)
                      "
                    ></i>
                  }
                }
              }

              @case ('textWithCaption') {
                @if (!loading) {
                  <span
                    class="textHead-style"
                    [ngClass]="getClassName(rowData, col)"
                    >{{ col.getText?.(rowData) }}</span
                  >
                  <span
                    class="text-style fontstyle"
                    [ngClass]="getClassName(rowData, col)"
                    >{{ col.getCaption?.(rowData) }}</span
                  >
                }
              }

              @case ('number') {
                <span
                  class="number text-style"
                  [ngClass]="getClassName(rowData, col)"
                  [innerHTML]="highlight(rowData[col.field])"
                >
                </span>
              }

              @case ('currency') {
                <span
                  class="number text-style"
                  [ngClass]="getClassName(rowData, col)"
                  [innerHTML]="highlight(rowData[col.field]) | currency"
                ></span>
              }

              @case ('percentage') {
                <span
                  class="number text-style"
                  [ngClass]="getClassName(rowData, col)"
                  [innerHTML]="
                    highlight(
                      rowData[col.field] + (rowData[col.field] ? '%' : '')
                    )
                  "
                ></span>
              }

              @case ('inputField') {
                @if (col.isEditable) {
                  <input
                    [(ngModel)]="rowData[col.field]"
                    [placeholder]="col.placeholder || ''"
                    class="form-control"
                    (ngModelChange)="onFieldDataChange(rowData, col, $event)"
                    [disabled]="!!col.isDisabled"
                  />
                } @else {
                  <span
                    class="text-style"
                    [innerHTML]="highlight(rowData[col.field])"
                  ></span>
                }
              }

@case ('inputFieldWithIcon') {
  @if (col.isEditable) {
    <div class="input-with-icon d-flex align-items-center position-relative">
      <input
        [(ngModel)]="rowData[col.field]"
        placeholder="Enter value..."
        class="form-control input-with-icon-field"
        (ngModelChange)="onFieldDataChange(rowData, col, $event)"
        [disabled]="!!col.isDisabled"
      />
      <i
        nz-icon
        nzType="save"
        nzTheme="fill"
        class="save-icon position-absolute"
        [class.disabled]="!rowData[col.field]"
        (click)="!rowData[col.field] ? null : onRowDataClick($event, rowData, 'save', rowIndex, getRowNumber(rowIndex))"
        nz-tooltip
        [nzTooltipTitle]="rowData[col.field] ? 'Save' : 'Enter a value to save'"
      ></i>
    </div>
  } @else {
    <span
      class="text-style"
      [innerHTML]="highlight(rowData[col.field])"
    ></span>
  }
}
              @case ('dropdownField') {
                @if (col.isEditable) {
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    nzPlaceHolder="--Select--"
                    [(ngModel)]="rowData[col.field]"
                    [nzDisabled]="!!col.isDisabled"
                    [nzMode]="
                      isMultiValuedDropdown(rowData[col.field])
                        ? 'multiple'
                        : 'default'
                    "
                  >
                    @for (item of col.items; track item.id) {
                      <nz-option
                        [nzLabel]="item.name"
                        [nzValue]="item.id"
                        [nzDisabled]="
                          item.disabledFn?.(rowData) || item.disabled
                        "
                        (ngModelChange)="
                          onFieldDataChange(rowData, col, $event)
                        "
                      ></nz-option>
                    }
                  </nz-select>
                } @else {
                  <span
                    class="text-style"
                    [innerHtml]="
                      highlight(
                        getValueById(
                          rowData[col.field],
                          col.items,
                          col.bindValue,
                          col.bindName,
                          col.field
                        )
                      )
                    "
                  ></span>
                }
              }

              @case ('checkbox') {
                <input
                  type="checkbox"
                  [(ngModel)]="rowData[col.field]"
                  (change)="onCheckboxChange(rowData, rowData[col.field])"
                />
              }

              @case ('actionFields') {
                <div class="action-container" *ngIf="col.actionFieldColumns">
                  @for (
                    actionCol of col.actionFieldColumns;
                    track actionCol.field
                  ) {
                    @if (
                      !actionCol?.conditionForShowActionField ||
                      actionCol.conditionForShowActionField?.(rowData)
                    ) {
                      @switch (actionCol.fieldType) {
                        @case ('button') {
                          @if (loading) {
                            <nz-skeleton-element
                              nzType="button"
                              [nzActive]="loading"
                            ></nz-skeleton-element>
                          } @else {
                            <div
                              nz-tooltip
                              [nzTooltipTitle]="
                                !isBtnDisabled(rowData, actionCol)
                                  ? actionCol.tooltipText
                                  : actionCol.getToolTipTextFn?.(rowData)
                              "
                              class="d-flex justify-content-center gap-4"
                            >
                              <button
                                nz-button
                                [nzGhost]="
                                  actionCol.secondaryBtnFn?.(rowData) ??
                                  actionCol.secondaryBtn
                                "
                                nzType="primary"
                                (click)="
                                  onRowDataClick(
                                    $event,
                                    rowData,
                                    actionCol.field,
                                    rowIndex,
                                    getRowNumber(rowIndex)
                                  )
                                "
                                [disabled]="isBtnDisabled(rowData, actionCol)"
                                [ngClass]="getClassName(rowData, actionCol)"
                              >
                                <i
                                  *ngIf="
                                    actionCol.getBtnIconClassFn?.(rowData) ||
                                    actionCol.buttonIconClass
                                  "
                                  [ngClass]="
                                    actionCol.getBtnIconClassFn?.(rowData) ||
                                    actionCol.buttonIconClass
                                  "
                                  class="me-2"
                                ></i>
                                {{
                                  actionCol.btnLabelFn?.(rowData) ??
                                    actionCol?.btnLabelText ??
                                    actionCol.field
                                }}
                              </button>
                            </div>
                          }
                        }

                        @case ('icon') {
                          @if (loading) {
                            <nz-skeleton-element
                              nzType="button"
                              [nzSize]="'small'"
                              [nzActive]="loading"
                              [nzShape]="'square'"
                            ></nz-skeleton-element>
                          } @else {
                            <span class="d-flex justify-content-center pointer">
                              <button
                                nz-button
                                nzType="text"
                                class="m-0 p-0"
                                nz-tooltip
                                [nzTooltipTitle]="
                                  actionCol.iconTooltipTitle ||
                                  actionCol.tooltipText
                                "
                                [nzTooltipPlacement]="
                                  actionCol?.tooltipPlacement
                                "
                                (click)="
                                  onRowDataClick(
                                    $event,
                                    rowData,
                                    actionCol.field,
                                    rowIndex,
                                    getRowNumber(rowIndex)
                                  )
                                "
                              >
                                @if (
                                  actionCol.showBadgeOverIconFn?.(rowData) ||
                                  actionCol.showBadgeOverIcon
                                ) {
                                  <div class="icon-container position-relative">
                                    <div class="badge-icon">
                                      <ng-template
                                        [ngTemplateOutlet]="iconTemplate"
                                        class="badge-icon"
                                        [ngTemplateOutletContext]="{
                                          iconType: actionCol.badgeIconType,
                                          nzIconType: actionCol.nzBadgeIconType,
                                          nzIconTheme:
                                            actionCol.nzBadgeIconTheme,
                                          svgIconPath: actionCol.svgIconPath,
                                          svgIconPathFn:
                                            actionCol.svgIconPathFn,
                                          iconClass: actionCol.iconClass,
                                          iconClasses: actionCol.iconClasses,
                                          rowData,
                                        }"
                                      ></ng-template>
                                    </div>
                                    <div>
                                      <ng-template
                                        [ngTemplateOutlet]="iconTemplate"
                                        [ngTemplateOutletContext]="{
                                          iconType: actionCol.iconType,
                                          nzIconType: actionCol.nzIconType,
                                          nzIconTheme: actionCol.nzIconTheme,
                                          svgIconPath: actionCol.svgIconPath,
                                          svgIconPathFn:
                                            actionCol.svgIconPathFn,
                                          iconClass: actionCol.iconClass,
                                          iconClasses: actionCol.iconClasses,
                                          primaryIcon: true,
                                          rowData,
                                        }"
                                      ></ng-template>
                                    </div>
                                  </div>
                                } @else {
                                  <ng-template
                                    [ngTemplateOutlet]="iconTemplate"
                                    [ngTemplateOutletContext]="{
                                      iconType: actionCol.iconType,
                                      nzIconType: actionCol.nzIconType,
                                      nzIconTheme: actionCol.nzIconTheme,
                                      svgIconPath: actionCol.svgIconPath,
                                      svgIconPathFn: actionCol.svgIconPathFn,
                                      iconClass: actionCol.iconClass,
                                      iconClasses: actionCol.iconClasses,
                                      primaryIcon: true,
                                      rowData,
                                    }"
                                  ></ng-template>
                                }
                              </button>
                            </span>
                          }
                        }

                        <!-- @case ('counterBtn') {
                          @if (loading) {
                            <nz-skeleton-element nzType="button" [nzShape]="'round'" [nzActive]="loading"></nz-skeleton-element>
                          } @else {
                            <div class="d-flex align-items-center rounded-2 gap-2">
                              <button nz-button nzType="text" (click)="onClickCounter(rowData, 'dec')">
                                <nz-icon nzType="minus-circle" nzTheme="outline" />
                              </button>
                              {{rowData?.count}}
                              <button nz-button nzType="text" (click)="onClickCounter(rowData, 'inc')">
                                <nz-icon nzType="plus-circle" nzTheme="outline" />
                              </button>
                            </div>
                          }
                        } -->

                        @case ('toggle') {
                          @if (loading) {
                            <nz-skeleton-element
                              nzType="button"
                              [nzSize]="'small'"
                              [nzActive]="loading"
                              [nzShape]="'round'"
                            ></nz-skeleton-element>
                          } @else {
                            <span
                              class="d-flex align-items-center justify-content-center pointer"
                            >
                              <nz-switch
                                [ngModel]="rowData.isActive"
                                [nzCheckedChildren]="'Active'"
                                [nzUnCheckedChildren]="'InActive'"
                                (click)="
                                  onRowDataClick(
                                    $event,
                                    rowData,
                                    actionCol.field,
                                    rowIndex,
                                    getRowNumber(rowIndex)
                                  )
                                "
                              ></nz-switch>
                            </span>
                          }
                        }

                        @case ('dropdownField') {
                          @if (actionCol.isEditable) {
                            <nz-select
                              nzShowSearch
                              nzAllowClear
                              nzPlaceHolder="--Select--"
                              nzAllowClear
                              [(ngModel)]="rowData[actionCol.field]"
                              [nzDisabled]="!!actionCol.isDisabled"
                              [nzMode]="
                                isMultiValuedDropdown(rowData[actionCol.field])
                                  ? 'multiple'
                                  : 'default'
                              "
                            >
                              @for (item of actionCol.items; track item.id) {
                                <nz-option
                                  [nzLabel]="item.name"
                                  [nzValue]="item.id"
                                  [nzDisabled]="
                                    item.disabledFn?.(rowData) || item.disabled
                                  "
                                  (ngModelChange)="
                                    onFieldDataChange(
                                      rowData,
                                      actionCol,
                                      $event
                                    )
                                  "
                                ></nz-option>
                              }
                            </nz-select>
                          } @else {
                            <span
                              class="text-style"
                              [innerHtml]="
                                highlight(
                                  getValueById(
                                    rowData[actionCol.field],
                                    actionCol.items,
                                    actionCol.bindValue,
                                    actionCol.bindName,
                                    actionCol.field
                                  )
                                )
                              "
                            ></span>
                          }
                        }
                      }
                    }
                  }
                </div>
              }
              @default {
                <span
                  class="text-style"
                  [ngClass]="getClassName(rowData, col)"
                  [innerHTML]="highlight(rowData[col.field])"
                ></span>
              }
            }
          </td>
        }
      </tr>
    }
  </tbody>
</nz-table>

<!-- Pagination templates -->
<ng-template #rangeTemplate let-range="range" let-total>
  Viewing {{ range[0] }}-{{ range[1] }} of {{ total }} records
</ng-template>

<!-- Icon Template -->
<ng-template
  #iconTemplate
  let-iconType="iconType"
  let-nzIconType="nzIconType"
  let-nzIconTheme="nzIconTheme"
  let-svgIconPathFn="svgIconPathFn"
  let-svgIconPath="svgIconPath"
  let-iconClass="iconClass"
  let-iconClasses="iconClasses"
  let-defaultColor="defaultColor"
  let-primaryIcon="primaryIcon"
  let-rowData="rowData"
>
  @switch (iconType) {
    @case ('nzIcon') {
      <nz-icon
        class="icons"
        [class.primary-icons]="primaryIcon"
        [nzType]="nzIconType || ''"
        [nzTheme]="nzIconTheme || 'outline'"
      >
      </nz-icon>
    }
    @case ('svgIcon') {
      <img
        [src]="svgIconPathFn?.(rowData) || svgIconPath"
        [alt]="svgIconPath"
        [ngClass]="iconClass || iconClasses"
      />
    }
    @case ('fa-icon') {
      <i
        [class.primary-icons]="primaryIcon"
        [ngClass]="iconClass || iconClasses"
      ></i>
    }
    @default {
      <i
        [class.primary-icons]="primaryIcon"
        [ngClass]="iconClass || iconClasses"
      ></i>
    }
  }
</ng-template>

<!-- 

  ╔═══════════════════════════════════════════════════════════════╗
  ║                       TableComponent Usage                    ║
  ╟───────────────────────────────────────────────────────────────╢
  ║  This is a generic table component designed for flexibility.  ║
  ║  Here's a sample usage of the component:                      ║
  ║                                                               ║
  ║  <app-table                                                   ║
  ║      [tableColumns]="columns"                                 ║
  ║      [tableData]="data"                                       ║
  ║      [loading]="isLoading"                                    ║
  ║      [showPagination]="true"                                  ║
  ║      [backendPagination]="false"                              ║
  ║      [backendSort]="false"                                    ║
  ║      [total]="toralRecordCount" /* for backend pagination */  ║
  ║      [(pageSize)]="pageSize"                                  ║
  ║      [(pageIndex)]="pageIndex"                                ║
  ║      [defaultSortOrder]="sortOrder"                           ║
  ║      [defaultSortParam]="sortParam"                           ║
  ║      [paginationPosition]="'top'"                             ║
  ║      [paginationHorizontalPosition]="'right'"                 ║
  ║      [isRowNumberVisible]="false"                             ║
  ║      (pageIndexChange)="onPageIndexChange($event)"            ║
  ║      (pageSizeChange)="onPageSizeChange($event)"              ║
  ║      (sortChange)="onSortChange($event)"                      ║
  ║      (tableDataClick)="onTableDataClick($event)"              ║
  ║      (fieldDataChange)="onFieldDataChange($event)" />         ║
  ║                                                               ║
  ║  where:                                                       ║
  ║    - `columns` is an array of ITableColumn<T>.                ║
  ║    - `data` is an array of table rows.                        ║
  ║    - `onTableDataClick` is triggered on row click.            ║
  ║    - `onFieldDataChange` is triggered on field updates.       ║
  ║                                                               ║
  ╚═══════════════════════════════════════════════════════════════╝

-->
