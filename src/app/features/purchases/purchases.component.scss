.container {
  width: 100%;
  margin: 0 auto;
}

.custom-wrapper {
  padding: 0 25px;
  width: 100%;
}

.wallet-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.company-info {
  h5 {
    margin-bottom: 0;
    font-weight: 600;
  }

  .subtext {
    color: #6c757d;
  }

  .wallet-label {
    margin-top: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .wallet-balance {
    margin-top: 0.5rem;
    font-size: 22px;
    font-weight: 600;
  }
}

.stats {
  text-align: right;

  .stats-row {
    display: flex;
    justify-content: flex-end;
    gap: 30px;
  }
}

::ng-deep .ant-collapse-item {
  background-color: #e9f4ff;
  border-radius: 10px;
  margin-bottom: 12px;
  border: none;
}

.page-title {
  margin-bottom: 2px;
}

.header-container {
  margin-bottom: 20px;
}

.content-area {
  padding: 0;
}

/* Transaction Card */
.transaction-card {
  border-radius: 8px;
  border: 1px solid #e7e7e7;

  transition: box-shadow 0.3s ease;
  cursor: pointer;
  background-color: #c2dfff;
  font-size: 14px;
  font-weight: 600;
  height: auto;
  min-height: 60px;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }

  ::ng-deep .ant-card-body {
    padding: 12px 16px;
  }

  .card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;

    .left {
      display: flex;
      flex-direction: column;
      gap: 4px;
      flex: 1;

      .transaction-id {
        font-size: 14px;
        font-weight: 400;
        color: #333;
        line-height: 1.2;
      }

      .amount {
        font-weight: 400;
        font-size: 14px;
        color: #000;
        line-height: 1.2;
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-shrink: 0;

      .date-time {
        text-align: right;

        .date {
          font-size: 14px;
          font-weight: 400;
          color: #333;
          line-height: 1.2;
          margin-bottom: 2px;
        }

        .time {
          font-size: 14px;
          font-weight: 400;
          color: #666;
          line-height: 1.2;
        }
      }

      .expand-icon {
        color: #999;
        font-size: 14px;
        transition: transform 0.3s;
        margin-left: 8px;
      }
    }
  }
}
.dropdown {
  border: none;
  margin-left: 12px;
}

.expanded-content {
  border-top: 1px solid #f0f0f0;

  .details {
    color: #666;
    font-size: 14px;
  }
}

:host ::ng-deep .ant-list-item {
  padding: 0;
}

/* Drawer */
.drawer-header {
  background: var(--primary-button-color);
  padding: 24px;
  color: white;
  border-bottom: 1px solid #e6f7ff;

  .user-info {
    display: flex;
    align-items: center;
    gap: 16px;

    .wallet-icon {
      font-size: 32px;
    }

    .user-details {
      .greeting {
        font-size: 16px;
        font-weight: 600;
      }

      .username {
        font-weight: 700;
        font-size: 18px;
      }

      .subtext {
        font-size: 12px;
        color: #e6f7ff;
      }
    }
  }
}

.drawer-body {
  padding: 24px;
  background-color: #fafafa;
}
.transaction-wrapper {
  border: 1px solid hsl(187, 100%, 98%); // light border
  background-color: #9cc5f1; // light sky-blue background
  padding: 20px;
  border-radius: 10px;
}
.transaction-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 1rem;

  h5 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    flex-shrink: 0;
  }
}
.search-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-radius: 7px;
  gap: 16px;

  .user-search,
  .txn-search,
  .date-picker {
    flex: 1;
    // min-width: 200px;
    height: 40px;
  }
}

.view-more-text {
  text-align: center;
  margin-top: 1rem;
  color: var(--primary-button-color);
  font-weight: 500;
  cursor: pointer;
  font-weight: bold;

  &:hover {
    text-decoration: underline;
  }
}
.transaction-wrapper {
  border: 1px solid #b3dffc; // light border
  background-color: #e9f4ff; // light sky-blue background
  padding: 20px;
  border-radius: 10px;
}
.view-wallet-history {
  font-weight: 500;
  font-size: 14px;
  color: #1d4ed8;
  text-decoration: underline;
  cursor: pointer;
  margin-top: 1rem;
  align-self: flex-end;

  &:hover {
    color: #0d3fcf;
  }
}

// Enforce consistent height and radius on all fields
// GLOBAL uniform input styles
/* Force height, padding, and border-radius on all input types */
::ng-deep .ant-input,
::ng-deep .ant-input-group-wrapper,
::ng-deep .ant-picker,
::ng-deep .ant-picker-range,
::ng-deep .ant-input-affix-wrapper {
  
  border-radius: 10px !important;
  font-size: 14px;
  
  line-height: 1.5715;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

/* Make suffix icons align properly */
::ng-deep .ant-input-suffix,
::ng-deep .ant-picker-suffix {
  color: #1d4ed8;
  display: flex;
  align-items: center;
}

/* Fix inconsistent box alignment inside group wrappers */
::ng-deep .ant-input-group-wrapper,
::ng-deep .ant-picker-range {
  display: flex;
  align-items: center;
}

/* Prevent nested wrapping/padding from causing height issues */
.search-group .user-search,
.search-group .txn-search,
.search-group .date-picker {
  flex: 1;
  height: 50px;
  display: flex;
  align-items: center;
  width: 100%;
}
