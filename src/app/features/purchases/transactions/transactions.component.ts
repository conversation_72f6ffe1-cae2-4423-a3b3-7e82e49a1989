import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  inject,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';

import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';

import { OrderItemDTO, TransactionDTO } from '../../../api-client';
import {
  TransactionService,
  TransactionWithOrder,
  DocumentInfo,
} from '../../../core/services/transaction.service';
import { UserService } from '../../../core/services/user.service';
import { NotificationService } from '../../../core/services/notification.service';

import { COMMON_STRINGS } from '../../../core/constants/common';
import { ROUTES } from '../../../core/constants/routes';
import { TRANSACTION_TABLE_COLUMNS } from '../../../core/tableColumns/transaction.column';

import { DataGridComponent } from '../../data-grid/data-grid.component';
import { ITableDataClickOutput } from '../../../core/interface/table';
import { ITransaction } from '../../../core/interface/transaction.interface';

@Component({
  selector: 'app-transactions',
  standalone: true,
  templateUrl: './transactions.component.html',
  styleUrl: './transactions.component.scss',
  imports: [
    CommonModule,
    RouterModule,
    DataGridComponent,
    NzButtonModule,
    NzIconModule,
    NzCardModule,
    NzBreadCrumbModule,
  ],
})
export class TransactionsComponent implements OnInit {
  @Input() transactionData: TransactionDTO[] = [];
  @Input() orderData: OrderItemDTO[] = [];
  @Input() fromPurchaseComp = false;
  @Output() collapseIconClick = new EventEmitter<void>();

  transactionId?: number;
  isLoading = false;
  tableColumns = TRANSACTION_TABLE_COLUMNS;
  currentTransactionWithOrder?: TransactionWithOrder;
  isExpanded = false;

  // Injected services
  private transactionService = inject(TransactionService);
  private userService = inject(UserService);
  private notificationService = inject(NotificationService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);

  ngOnInit(): void {
    // Skip loading if data is already provided via input
    if (this.transactionData.length > 0) {
      // If we have transaction data, try to get order details for document functionality
      if (this.transactionData[0]?.transactionId) {
        this.loadOrderDetails(this.transactionData[0].transactionId);
      }
      return;
    }

    // Check for ID in route param or state
    this.transactionId = this.route.snapshot.params['transactionId'];
    const transactionDetails =
      this.router.getCurrentNavigation()?.extras.state?.['transactionDetails'];

    if (transactionDetails) {
      this.transactionData = [transactionDetails];
      if (transactionDetails.transactionId) {
        this.loadOrderDetails(transactionDetails.transactionId);
      }
    } else if (this.transactionId) {
      this.loadTransactionWithOrder(this.transactionId);
    }
  }

  // Navigate back to Purchases page
  onClickPurchases(): void {
    this.router.navigate([ROUTES.sidebar.purchases]);
  }

  // Fetch transaction with order details from backend
  private loadTransactionWithOrder(id: number): void {
    this.isLoading = true;
    this.transactionService.getTransactionWithOrder(id).subscribe({
      next: (response) => {
        this.currentTransactionWithOrder = response;
        this.transactionData = [response];
        this.loadOrderDetails(this.transactionData[0].transactionId!);

        this.isLoading = false;
      },
      error: (err) => {
        console.error('Failed to fetch transaction with order:', err.message);
        this.notificationService.error(
          COMMON_STRINGS.errorMessages.failedToFetchTransactions,
        );
        this.isLoading = false;
      },
    });
  }
  private loadOrderDetails(transactionId: number): void {
    this.isLoading = true;
    this.transactionService.getOrderByTransactionId(transactionId).subscribe({
      next: (orderResponse) => {
        this.isLoading = false;
        const documents: DocumentInfo[] = [];

        if (orderResponse?.items) {
          orderResponse.items.forEach((item) => {
            if (item.document) {
              documents.push({
                filePath: item.document.filePath || '',
                title: item.document.title || item.productName || 'Document',
                description: item.document.description,
                productName: item.productName,
                expiryDate: item.document.expiryDate,
                issueDate: item.document.createdAt,
                finalPrice: item.finalPrice,
                transactionId: transactionId,
                transactionDate: this.transactionData[0].transactionDate,
                referenceId: this.transactionData[0].referenceId,
                documentType: item.documentType,
              });
            }
          });
        }

        this.currentTransactionWithOrder = {
          ...this.transactionData[0],
          orderDetails: orderResponse,
          documents: documents,
          hasDocuments: documents.length > 0,
        };
      },
      error: (err) => {
        this.isLoading = false;
        console.error('Failed to fetch order details:', err.message);
      },
    });
  }

  // Fetch transaction from backend (legacy method - keeping for backward compatibility)
  private loadTransactionById(id: number): void {
    this.isLoading = true;
    this.transactionService.getTransactionById(id).subscribe({
      next: (response) => {
        this.transactionData = [response];
        this.isLoading = false;
        // Also load order details
        this.loadOrderDetails(id);
      },
      error: (err) => {
        console.error('Failed to fetch transaction:', err.message);
        this.notificationService.error(
          COMMON_STRINGS.errorMessages.failedToFetchTransactions,
        );
        this.isLoading = false;
      },
    });
  }

  // Return current user's first name
  getCurrentUser(): string {
    return this.userService.userInfo?.firstName ?? 'John Doe';
  }

  // Invoice-related actions
  emailInvoice(): void {
    this.notificationService.success(
      COMMON_STRINGS.successMessages.emailInvoiceSuccess,
    );
  }

  downloadInvoice(): void {
    this.notificationService.success(
      COMMON_STRINGS.successMessages.downloadInvoiceSuccess,
    );
  }

  // Document-related actions
  viewDocuments(): void {
    if (
      this.currentTransactionWithOrder?.documents &&
      this.currentTransactionWithOrder.documents.length > 0
    ) {
      if (this.currentTransactionWithOrder.documents.length === 1) {
        // Single document - open directly
        this.transactionService.viewDocument(
          this.currentTransactionWithOrder.documents[0].filePath,
        );
        this.notificationService.success(
          COMMON_STRINGS.successMessages.openDocumentsSuccess,
        );
      } else {
        // Multiple documents - open all
        this.transactionService.viewAllDocuments(
          this.currentTransactionWithOrder.documents,
        );
        this.notificationService.success(
          COMMON_STRINGS.successMessages.openDocumentsSuccess,
        );
      }
    } else {
      this.notificationService.warning(
        COMMON_STRINGS.errorMessages.failedToFetchDocuments,
      );
    }
  }

  downloadDocument(): void {
    if (
      this.currentTransactionWithOrder?.documents &&
      this.currentTransactionWithOrder.documents.length > 0
    ) {
      if (this.currentTransactionWithOrder.documents.length === 1) {
        // Single document - download directly
        const doc = this.currentTransactionWithOrder.documents[0];
        const fileName = `transaction_${this.currentTransactionWithOrder.transactionId}_${doc.productName || 'document'}.pdf`;
        this.transactionService.downloadDocument(doc.filePath, fileName);
        this.notificationService.success(
          COMMON_STRINGS.successMessages.downloadInvoiceSuccess,
        );
      } else {
        // Multiple documents - download all
        this.transactionService.downloadAllDocuments(
          this.currentTransactionWithOrder.documents,
          this.currentTransactionWithOrder.transactionId || 0,
        );
        this.notificationService.success(
          COMMON_STRINGS.successMessages.downloadInvoiceSuccess,
        );
      }
    } else {
      this.notificationService.warning(
        COMMON_STRINGS.errorMessages.failedToFetchDocuments,
      );
    }
  }

  // Get document count for display
  getDocumentCount(): number {
    return this.currentTransactionWithOrder?.documents?.length || 0;
  }

  // Get document titles for display
  getDocumentTitles(): string {
    if (!this.currentTransactionWithOrder?.documents) return '';

    const titles = this.currentTransactionWithOrder.documents.map(
      (doc) => doc.title || doc.productName || 'Document',
    );

    if (titles.length <= 2) {
      return titles.join(', ');
    } else {
      return `${titles.slice(0, 2).join(', ')} and ${titles.length - 2} more`;
    }
  }

  // Check if documents are available
  hasDocuments(): boolean {
    return this.currentTransactionWithOrder?.hasDocuments || false;
  }

  onCollapseIconClick(): void {
    this.collapseIconClick.emit();
    this.isExpanded = !this.isExpanded;
  }

  get totalAmount(): number {
    return this.transactionData.reduce(
      (acc, curr) => acc + (curr?.amount ?? 0),
      0,
    );
  }

  onTableDataClick(data: ITableDataClickOutput<ITransaction>): void {
    const { actionField } = data;
    switch (actionField) {
      case 'view':
        this.viewDocuments();
        break;
      case 'Download':
        this.downloadDocument();
        break;
      default:
        this.notificationService.error(
          COMMON_STRINGS.warningMessages.addToCartWarning,
        );
        break;
    }
  }
}
