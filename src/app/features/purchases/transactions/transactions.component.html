@if (transactionId) {
  <nz-breadcrumb class="ms-5 mt-2">
    <nz-breadcrumb-item>
      <a (click)="onClickPurchases()">My Transactions</a>
    </nz-breadcrumb-item>
    <nz-breadcrumb-item>
      <a>{{ transactionData[0].transactionId }}</a>
    </nz-breadcrumb-item>
  </nz-breadcrumb>
}

<!-- 🔧 Wrapper to add left-right spacing -->
<div class="transaction-wrapper" [class.m-5]="!fromPurchaseComp" class="mt-4">
  <div
    *ngIf="transactionData.length > 0"
    class="custom-transaction-card w-100 mb-2"
  >
    <!-- Header Banner -->
    <div
      class="header-banner text-white px-3 py-2 rounded-top"
      [class.pointer]="fromPurchaseComp"
      (click)="onCollapseIconClick()"
    >
      <div class="col-icon">
        <div>Transaction ID - {{ transactionData[0].transactionId }}</div>
        <div class="me-3" *ngIf="fromPurchaseComp && isExpanded">
          <i
            nz-icon
            [nzType]="'down'"
            nzTheme="outline"
            (click)="onCollapseIconClick()"
            class="expand-icon"
          ></i>
        </div>
      </div>
      <div class="date text-white px-3 py-2 rounded-top">
        Date -
        {{ transactionData[0].transactionDate | date: 'MMM d, y' }}
        {{ transactionData[0].transactionDate | date: 'h:mm a' }}
      </div>
    </div>

    <!-- Card Body Content -->
    <div class="p-3 rounded-bottom">
      <!-- Aligned Buttons Row -->
      <div class="d-flex justify-content-between align-items-center mb-3">
        <!-- Left side buttons -->
        <div class="d-flex gap-3">
          <button
            nz-button
            nzType="primary"
            nzGhost
            (click)="downloadDocument()"
          >
            Download
          </button>
          <button nz-button nzType="primary" (click)="viewDocuments()">
            View Documents
          </button>
        </div>

        <!-- Right side button -->
        <div>
          <button nz-button nzType="primary" nzGhost (click)="emailInvoice()">
            Email Invoice
          </button>
        </div>
      </div>

      <!-- Data Grid Table -->
      <div class="wrapper mx-1">
        <app-data-grid
          [tableData]="currentTransactionWithOrder?.documents || []"
          [tableColumns]="tableColumns"
          [loading]="isLoading"
          [showPagination]="false"
          [frontPagination]="false"
          [showSizeChanger]="true"
          [showQuickJumper]="true"
          [showTotal]="true"
          [borderedGrid]="true"
          [scrollY]="null"
          [headerTextAlign]="'start'"
          (tableDataClick)="onTableDataClick($event)"
           
        ></app-data-grid>
      </div>
    </div>

    <div class="total">
      <p class="text-muted"></p>
      <p class="total-amount fs-2">Total {{ totalAmount | currency: 'USD' }}</p>
    </div>
  </div>
</div>
