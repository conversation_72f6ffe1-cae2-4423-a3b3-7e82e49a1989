<!-- Top-level wrapper -->
<div class="custom-wrapper">
  <!-- Content area -->
  <div class="content-area">
    <!-- Wallet Info -->
    <div class="wallet-header">
      <div class="company-info">
        <div class="wallet-label">
          Current Wallet Balance <i class="fas fa-wallet"></i>
        </div>
        <div class="wallet-balance">$ {{ walletBalance.toFixed(2) }}</div>
        <div
          class="view-wallet-history"
          *ngIf="
            (getCurrentUserRole() === enumUserRole.COMPANY_ADMIN ||
             getCurrentUserRole() === enumUserRole.INDEPENDEDNT_AGENT) &&
            !companyId
          "
          (click)="openWalletHistory()"
        >
          View Wallet History
        </div>
      </div>

      <div class="stats">
        <!-- Show Recharge button for COMPANY_ADMIN or INDEPENDEDNT_AGENT when no companyId -->
        <button
          *ngIf="
            (getCurrentUserRole() === enumUserRole.COMPANY_ADMIN ||
             getCurrentUserRole() === enumUserRole.INDEPENDEDNT_AGENT) &&
            !companyId
          "
          nz-button
          nzType="primary"
          class="mt-3"
          (click)="drawerVisible = true"
        >
          Recharge
        </button>
        <!-- Show Wallet History button for PLATFORM_ADMIN when companyId is provided -->
        <button
          *ngIf="companyId"
          nz-button
          nzType="primary"
          class="mt-3"
          (click)="openWalletHistory()"
        >
          Wallet History
        </button>
        <nz-drawer
          [nzVisible]="drawerVisible"
          nzTitle="Recharge Wallet"
          nzPlacement="right"
          [nzClosable]="true"
          (nzOnClose)="drawerVisible = false"
          [nzExtra]="extraForWallet"
          [nzWidth]="400"
          [nzBodyStyle]="{ padding: '0px' }"
        >
          <ng-container *nzDrawerContent>
            <app-wallet
              [(amount)]="amount"
              [visibleWallet]="drawerVisible"
              (visibleWalletChange)="drawerVisible = $event"
              (visibleWalletAfterDelay)="onDrawerCloseAfterDelay($event)"
            ></app-wallet>
          </ng-container>
        </nz-drawer>
      </div>
    </div>

    <hr />
    <div class="transaction-wrapper">
      <!-- Heading and Search Side by Side -->
      <div class="transaction-header">
        <h5>Transactions</h5>
        <div class="search-group">
          <!-- User Search (if COMPANY_ADMIN and no companyId) -->
          <div
            *ngIf="
              getCurrentUserRole() === enumUserRole.COMPANY_ADMIN && !companyId
            "
            class="user-search"
          >
            <nz-input-group [nzSuffix]="searchIcon">
              <input
                type="text"
                nz-input
                placeholder="Search Users"
                [(ngModel)]="searchUsers"
                (ngModelChange)="searchTransaction()"
              />
            </nz-input-group>
          </div>

          <!-- Transaction Search -->
          <div class="txn-search">
            <nz-input-group [nzSuffix]="searchIcon">
              <input
                type="text"
                nz-input
                placeholder="Find Transactions"
                [(ngModel)]="searchText"
                (ngModelChange)="searchTransaction()"
              />
            </nz-input-group>
          </div>

          <div class="date-picker">
            <nz-range-picker
              [(ngModel)]="selectedDateRange"
              (ngModelChange)="onDateChange($event)"
              [nzFormat]="'dd/MM/yyyy'"
              [nzAllowClear]="true"
              [nzShowToday]="true"
              [nzShowNow]="true"
              nzShowTime="false"
            ></nz-range-picker>
          </div>
        </div>
        <ng-template #searchIcon>
          <i nz-icon nzType="search"></i>
        </ng-template>
      </div>

      <!-- Transaction List -->

      <nz-list
        [nzDataSource]="visibleTransactions"
        [nzRenderItem]="item"
        [nzBordered]="false"
        nzItemLayout="vertical"
        [class.p-0]="true"
        [nzLoading]="isLoading"
      >
        <ng-template #item let-transactions>
          @if (isLoading) {
            <nz-skeleton
              [nzActive]="isLoading"
              [nzParagraph]="{ rows: 1, width: '90%' }"
            ></nz-skeleton>
          } @else {
            @if (!transactions.expanded) {
              <nz-card
                class="transaction-card mb-2"
                [class.expanded]="transactions.expanded"
                [nzBordered]="false"
                (click)="toggleExpand(transactions)"
              >
                <div class="card-content">
                  <div class="left">
                    <div class="transaction-id">
                      <strong>Transaction ID:</strong>
                      {{ transactions.id || transactions.transactionId }}
                    </div>
                    <div class="amount" [ngClass]="{
                      'positive': transactions.transactionType === enumTransactionTypes.RECHARGE,
                      'negative': transactions.transactionType === enumTransactionTypes.PURCHASE
                    }">
                    {{ transactions.transactionType === enumTransactionTypes.RECHARGE ? '+' : '-' }} ${{ transactions.amount }}
                  </div>
                  
                  </div>
                  <div class="right">
                    <div class="date-time">
                      <div class="date">
                        <strong>Date:</strong>
                        {{ transactions.transactionDate | date: 'MMM d, y' }}
                      </div>
                      <div class="time">
                        <strong>Time:</strong>
                        {{ transactions.transactionDate | date: 'h:mm a' }}
                      </div>
                    </div>
                    <i
                      nz-icon
                      [nzType]="transactions.expanded ? 'down' : 'right'"
                      nzTheme="outline"
                      class="expand-icon"
                    ></i>
                  </div>
                </div>
              </nz-card>
            } @else {
              <div class="expanded-content">
                <div class="details">
                  <app-transactions
                    [transactionData]="[transactions]"
                    [fromPurchaseComp]="true"
                    (collapseIconClick)="toggleExpand(transactions)"
                  ></app-transactions>
                </div>
              </div>
            }
          }
        </ng-template>
      </nz-list>

      <!-- View More -->
      <div
        class="view-more-text"
        *ngIf="!showAll && visibleTransactions.length < transactionData.length"
        (click)="loadMore()"
      >
        View More
      </div>
    </div>
  </div>
  <nz-drawer
    [nzClosable]="false"
    [nzVisible]="showWalletHistory"
    nzPlacement="right"
    [nzTitle]="drawerTitle"
    [nzExtra]="extraForWalletHistory"
    (nzOnClose)="showWalletHistory = false"
    [nzWidth]="400"
    [nzBodyStyle]="{ padding: '0px' }"
  >
    <ng-container *nzDrawerContent>
      <div class="dropdown">
        <app-data-grid
          [tableColumns]="WALLET_HISTORY_TABLE_COLUMNS"
          [tableData]="rechargeTransactions"
          [loading]="isLoading"
          [showPagination]="false"
          [headerBorder]="false"
          [borderedGrid]="false"
          headerBgColor="var(--primary-application-color, #AAD3FF)"
          color="black"
          [evenRowdarker]="false"
          [stripAtLastColumn]="false"
          [rowBorder]="true"
          [scrollY]="null"
           
        >
        </app-data-grid>
      </div>
    </ng-container>
  </nz-drawer>
</div>

<ng-template #extraForWallet>
  <button nz-button nzType="primary" nzGhost (click)="closeWalletDrawer()">
    <nz-icon
      data-testid="wallet-close-drawer"
      nzType="close-circle"
      nzTheme="fill"
    />
    Close
  </button>
</ng-template>
<ng-template #extraForWalletHistory>
  <button
    nz-button
    nzType="primary"
    nzGhost
    (click)="closeWalletHistoryDrawer()"
  >
    <nz-icon
      data-testid="close-wallet-history-drawer"
      nzType="close-circle"
      nzTheme="fill"
    />
    Close
  </button>
</ng-template>
<ng-template #drawerTitle>
  <h2>Wallet History</h2>
</ng-template>

