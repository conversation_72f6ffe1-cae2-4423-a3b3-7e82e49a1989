import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import {
  ApiResponsePageUserResponseDTO,
  UserResponseDTO,
  AddressResponseDTO,
} from '../../api-client';
import { RegisterService } from '../../core/services/register.service';
import { CommonModule } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NotificationService } from '../../core/services/notification.service';
import { COMMON_STRINGS } from '../../core/constants/common';
import { DataGridComponent } from '../data-grid/data-grid.component';
import { MANAGE_USER_TABLE_COLUMNS } from '../../core/tableColumns/users.column';
import { IUserFields } from '../../core/interface/user-fields';
import { Subscription } from 'rxjs';
import { ITableDataClickOutput } from '../../core/interface/table';
import { HttpErrorResponse } from '@angular/common/http';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { AddUserComponent } from '../company-management/add-user/add-user.component';
import { ICompanyFields } from '../../core/interface/company-fields';

@Component({
  selector: 'app-manage-users',
  standalone: true,
  imports: [
    CommonModule,
    NzTableModule,
    NzButtonModule,
    DataGridComponent,
    NzModalModule,
    AddUserComponent,
  ],
  templateUrl: './manage-users.component.html',
  styleUrls: ['./manage-users.component.scss'],
})
export class ManageUsersComponent implements OnInit {
  @Input() userId?: number;
  @Input() companyId?: number;
  @Input() companyInfo: ICompanyFields | null | undefined = null;
  @Input() primaryAddress?: AddressResponseDTO;
  @Input() billingAddress?: AddressResponseDTO;
  users: UserResponseDTO[] = [];
  manageUserTableColumns = MANAGE_USER_TABLE_COLUMNS;
  manageActiveUsersTableData: IUserFields[] = [];
  manageInactiveUsersTableData: IUserFields[] = [];
  isLoading = false;
  private subscriptions = new Subscription();
  isManageInactiveUsersAccordionOpen = false;
  isAddUserFormVisible = false;
  userToEdit?: IUserFields;

  constructor(
    private registerService: RegisterService,
    private notification: NotificationService,
    private modal: NzModalService,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    if (this.companyId == null) {
      this.notification.error(
        COMMON_STRINGS.warningMessages.companyIdNotAvailable,
      );
      return;
    }
    this.fetchUsers();
  }

  fetchUsers(): void {
    if (!this.companyId) {
      this.notification.error(
        COMMON_STRINGS.warningMessages.companyIdNotAvailable,
      );
      return;
    }

    this.isLoading = true;
    this.subscriptions.add(
      this.registerService.getAllUsers(this.companyId).subscribe({
        next: (response: ApiResponsePageUserResponseDTO) => {
          this.users = response.data?.content || [];

          // Map UserResponseDTO to IUserFields
          const mappedUsers: IUserFields[] = this.users.map((user) => ({
            id: user.id,
            primaryAddress: user.primaryAddress,
            billingAddress: user.billingAddress,
            fullName:
              `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'N/A',
            email: user.email || 'N/A',
            roleDisplayText: user.roleDisplayText || 'N/A',
            companyId: user.companyId,
            isActive: user.isActive,
            firstName: user.firstName || '',
            lastName: user.lastName || '',
            contactNumber: user.contactNumber || '',
            roleId: user.roleId,
            profilePictureUrl: user.profilePictureUrl, // Match UserResponseDTO type
          }));

          // Filter into active and inactive users
          this.manageActiveUsersTableData = mappedUsers.filter(
            (user) => user.isActive,
          );
          this.manageInactiveUsersTableData = mappedUsers.filter(
            (user) => !user.isActive,
          );

          // Check for companyId mismatch
          if (mappedUsers.some((user) => user.companyId !== this.companyId)) {
            this.notification.warning(
              COMMON_STRINGS.warningMessages.userWarning,
            );
          }

          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: (err) => {
          this.isLoading = false;
          this.notification.error(
            COMMON_STRINGS.errorMessages.failedToFetchUsers,
          );
          console.error('Error fetching users:', err);
        },
      }),
    );
  }

  onTableDataClick(data: ITableDataClickOutput<IUserFields>): void {
    const { actionField, rowData } = data;
    switch (actionField) {
      case 'edit':
        this.editUser(rowData);
        break;
      case 'toggle':
        this.activeInactiveUser(rowData);
        break;
    }
  }

  editUser(rowData: IUserFields): void {
    this.userToEdit = {
      ...rowData,
      firstName: rowData.firstName || '',
      lastName: rowData.lastName || '',
      email: rowData.email || '',
      contactNumber: rowData.contactNumber || '',
      roleId: rowData.roleId,
      profilePictureUrl: rowData.profilePictureUrl,
    };
    this.isAddUserFormVisible = true;
    this.cdr.detectChanges();
  }

  activeInactiveUser(rowData: IUserFields): void {
    if (!rowData.id) {
      this.notification.error(COMMON_STRINGS.errorMessages.userIdNotFound);
      return;
    }

    const isCurrentlyActive = rowData.isActive;
    const actionText = isCurrentlyActive ? 'deactivate' : 'activate';
    const userName = rowData.fullName || 'User';
    const confirmMessage =
      COMMON_STRINGS.confirmMessages.toggleUserStatusConfirmation
        .replace('${actionText}', actionText)
        .replace('${userName}', userName);
    const confirmButtonText = isCurrentlyActive
      ? COMMON_STRINGS.dialogConfigurations.buttonLabels.ConfirmDeactivate
      : COMMON_STRINGS.dialogConfigurations.buttonLabels.ConfirmActivate;

    this.modal.confirm({
      nzTitle: confirmMessage,
      nzOkText: confirmButtonText,
      nzOkType: 'primary',
      nzOkDanger: isCurrentlyActive,
      nzCancelText: COMMON_STRINGS.dialogConfigurations.buttonLabels.Cancel,
      nzOnOk: () => {
        this.isLoading = true;
        this.registerService
          .activeInactiveUser(rowData.id!, !isCurrentlyActive)
          .subscribe({
            next: (response) => {
              this.isLoading = false;
              if (response.success) {
                if (isCurrentlyActive) {
                  this.makeUserInactive(rowData);
                } else {
                  this.makeUserActive(rowData);
                }
                this.notification.success(
                  COMMON_STRINGS.successMessages.userToggleSuccess.replace(
                    '${actionText}',
                    actionText,
                  ),
                );
              } else {
                this.notification.error(
                  COMMON_STRINGS.errorMessages.failedToToggleUser
                    .replace('${actionText}', actionText)
                    .replace(
                      '${errorMessage}',
                      response.message || 'Unknown error',
                    ),
                );
              }
            },
            error: (error: HttpErrorResponse) => {
              this.isLoading = false;
              this.notification.error(
                `Failed to ${actionText} user: ${error.error?.message || error.message || 'Network error'}`,
              );
            },
          });
      },
    });
  }

  private makeUserInactive(rowData: IUserFields): void {
    this.manageActiveUsersTableData = this.manageActiveUsersTableData.filter(
      (user) => user.id !== rowData.id,
    );
    const userAlreadyExists = this.manageInactiveUsersTableData.some(
      (user) => user.id === rowData.id,
    );

    if (!userAlreadyExists) {
      this.manageInactiveUsersTableData = [
        ...this.manageInactiveUsersTableData,
        { ...rowData, isActive: false },
      ];
    }
    this.cdr.detectChanges();
  }

  private makeUserActive(rowData: IUserFields): void {
    this.manageInactiveUsersTableData =
      this.manageInactiveUsersTableData.filter(
        (user) => user.id !== rowData.id,
      );

    const userAlreadyExists = this.manageActiveUsersTableData.some(
      (user) => user.id === rowData.id,
    );

    if (!userAlreadyExists) {
      this.manageActiveUsersTableData = [
        ...this.manageActiveUsersTableData,
        { ...rowData, isActive: true },
      ];
    }
    this.cdr.detectChanges();
  }

  toggleDeletedUsersAccordion(): void {
    this.isManageInactiveUsersAccordionOpen =
      !this.isManageInactiveUsersAccordionOpen;
    this.cdr.detectChanges();
  }

  addUserFormVisible(): void {
    if (this.companyId == null) {
      this.notification.error(COMMON_STRINGS.errorMessages.companyIdIsRequired);
      return;
    }
    this.userToEdit = undefined;
    this.isAddUserFormVisible = true;
    this.cdr.detectChanges();
  }

  onDrawerClosed(): void {
    this.isAddUserFormVisible = false;
    this.userToEdit = undefined;
    this.cdr.detectChanges();
  }

  onUserAdded(newUser: IUserFields): void {
    if (!newUser?.id) {
      this.notification.error(COMMON_STRINGS.errorMessages.failedToAddUser);
      this.isAddUserFormVisible = false;
      return;
    }
    const userAlreadyExists = this.manageActiveUsersTableData.some(
      (user) => user.id === newUser.id,
    );
    if (!userAlreadyExists) {
      this.manageActiveUsersTableData = [
        { ...newUser, isActive: true },
        ...this.manageActiveUsersTableData,
      ];
      this.notification.success(
        COMMON_STRINGS.successMessages.userAddedToActiveUsers,
      );
      this.cdr.detectChanges();
    } else {
      this.notification.warning(
        COMMON_STRINGS.warningMessages.userAlreadyExists,
      );
    }
    this.isAddUserFormVisible = false;
  }

  onUserUpdated(updatedUser: IUserFields): void {
    if (!updatedUser?.id) {
      this.notification.error(COMMON_STRINGS.errorMessages.failedToUpdateUser);
      this.isAddUserFormVisible = false;
      return;
    }
    this.manageActiveUsersTableData = this.manageActiveUsersTableData.map(
      (user) =>
        user.id === updatedUser.id
          ? { ...updatedUser, isActive: user.isActive }
          : user,
    );
    this.manageInactiveUsersTableData = this.manageInactiveUsersTableData.map(
      (user) =>
        user.id === updatedUser.id
          ? { ...updatedUser, isActive: user.isActive }
          : user,
    );
    this.notification.success(COMMON_STRINGS.successMessages.userUpdateSuccess);
    this.isAddUserFormVisible = false;
    this.cdr.detectChanges();
  }
}
