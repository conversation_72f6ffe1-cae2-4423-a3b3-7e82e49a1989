import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';

import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzUploadModule } from 'ng-zorro-antd/upload';

import { Subscription } from 'rxjs';

import {
  CompanyDTO,
  StateDTO,
  ZipCodeDTO,
  ApiResponseCompanyDTO,
  CompanyRequestDTO,
} from '../../../api-client';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { ICompanyFields } from '../../../core/interface/company-fields';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { CompanyValidation } from '../../../core/components/company-validation';
import { CompanyState } from '../../../core/components/company-state';
import { CompanyForm } from '../../../core/components/company-form';

@Component({
  selector: 'app-edit-company-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NzTabsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzCardModule,
    NzTableModule,
    NzModalModule,
    NzUploadModule,
    NzIconModule,
    NzDrawerModule,
    NzSelectModule,
    NzBreadCrumbModule,
  ],
  templateUrl: './edit-company-form.component.html',
  styleUrls: [
    './edit-company-form.component.scss',
    '../view-company/view-company.component.scss',
  ],
})
export class EditCompanyFormComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() company: ICompanyFields | null = null;
  @Output() companySaved = new EventEmitter<CompanyDTO>();
  @Output() formCancelled = new EventEmitter<void>();

  companyForm: FormGroup;
  isLoading = false;
  isBillingDetailsAccordionOpen = false;
  isSameAsCompanyDetails = false;

  states: StateDTO[] = [];
  postalCode: ZipCodeDTO[] = [];
  billingZipcode: ZipCodeDTO[] = [];

  private subscriptions = new Subscription();

  constructor(
    private notification: NotificationService,
    private registerService: RegisterService,
    private companyStateService: CompanyState,
    private cdr: ChangeDetectorRef,
    private companyFormService: CompanyForm,
    private companyValidationService: CompanyValidation,
  ) {
    this.companyForm = this.companyFormService.createEditCompanyForm();
  }

  async ngOnInit(): Promise<void> {
    if (this.company) {
      await this.initializeForm();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  get isSaveButtonEnabled(): boolean {
    return this.companyValidationService.isSaveButtonEnabled(
      this.companyForm,
      this.isLoading,
    );
  }

  async initializeForm(): Promise<void> {
    if (!this.company) return;

    await this.fetchStates();
    this.populateFormWithCompanyData();
    await this.loadZipcodesForStates();
    this.setupFormSubscriptions();
    this.companyForm.markAsPristine();
    this.cdr.detectChanges();
  }

  private async loadZipcodesForStates(): Promise<void> {
    const primaryStateId = this.companyForm.get('state')?.value;
    const billingStateId = this.companyForm.get('billingState')?.value;

    const promises = [
      primaryStateId
        ? this.fetchZipcodes(primaryStateId, 'primary')
        : Promise.resolve(),
      billingStateId && !this.isSameAsCompanyDetails
        ? this.fetchZipcodes(billingStateId, 'billing')
        : Promise.resolve(),
    ].filter((p) => p !== Promise.resolve());

    await Promise.all(promises);
  }

  private populateFormWithCompanyData(): void {
    if (!this.company) return;

    this.companyFormService.populateFormWithCompanyData(
      this.companyForm,
      this.company,
      this.states,
    );
    this.isSameAsCompanyDetails = false;
    this.enableBillingFields();

    setTimeout(() => this.companyForm.markAsPristine(), 100);
    this.cdr.detectChanges();
  }

  private setupFormSubscriptions(): void {
    this.subscriptions.add(
      this.companyForm.get('state')?.valueChanges.subscribe((stateId) => {
        if (stateId) this.handleStateChange(stateId, 'primary');
      }),
    );

    this.subscriptions.add(
      this.companyForm
        .get('billingState')
        ?.valueChanges.subscribe((stateId) => {
          if (stateId && !this.isSameAsCompanyDetails)
            this.handleStateChange(stateId, 'billing');
        }),
    );

    this.subscriptions.add(
      this.companyForm.valueChanges.subscribe(() => this.cdr.detectChanges()),
    );
  }

  private async handleStateChange(
    stateId: number,
    addressType: 'primary' | 'billing',
  ): Promise<void> {
    await this.fetchZipcodes(stateId, addressType);
    this.resetPostalCodeForAddressType(addressType);
    if (addressType === 'primary' && this.isSameAsCompanyDetails) {
      this.syncBillingZipcodes();
    }
    this.cdr.detectChanges();
  }

  private resetPostalCodeForAddressType(
    addressType: 'primary' | 'billing',
  ): void {
    const controlName =
      addressType === 'primary' ? 'postalCode' : 'billingPostalCode';
    this.companyForm.get(controlName)?.setValue('', { emitEvent: false });
  }

  private syncBillingZipcodes(): void {
    this.companyForm
      .get('billingPostalCode')
      ?.setValue('', { emitEvent: false });
    this.billingZipcode = this.postalCode;
  }

  async fetchStates(): Promise<void> {
    this.isLoading = true;
    return new Promise((resolve, reject) => {
      this.subscriptions.add(
        this.companyFormService.fetchStates().subscribe({
          next: (states: StateDTO[]) => {
            this.isLoading = false;
            this.states = states;
            resolve();
          },
          error: (error: unknown) => {
            this.isLoading = false;
            console.error('Failed to fetch states:', error);
            reject();
          },
        }),
      );
    });
  }

  async fetchZipcodes(
    stateId: number,
    addressType: 'primary' | 'billing',
  ): Promise<void> {
    if (!stateId) return;

    this.isLoading = true;
    return new Promise((resolve, reject) => {
      this.subscriptions.add(
        this.companyFormService.fetchZipcodes(stateId).subscribe({
          next: (zipcodes: ZipCodeDTO[]) => {
            this.isLoading = false;
            this.processZipcodes(zipcodes, addressType);
            this.handleZipcodesResult(zipcodes);
            resolve();
          },
          error: (error: unknown) => {
            this.isLoading = false;
            console.error(`Failed to fetch ${addressType} zipcodes:`, error);
            reject();
          },
        }),
      );
    });
  }

  private processZipcodes(
    zipcodes: ZipCodeDTO[],
    addressType: 'primary' | 'billing',
  ): void {
    if (addressType === 'primary') {
      this.postalCode = zipcodes;
      this.setMatchingZipcode(
        'postalCode',
        this.company?.primaryAddress?.zipCodeId,
        zipcodes,
      );
    } else {
      this.billingZipcode = zipcodes;
      this.setMatchingZipcode(
        'billingPostalCode',
        this.company?.billingAddress?.zipCodeId,
        zipcodes,
      );
    }
  }

  private setMatchingZipcode(
    controlName: string,
    zipCodeId: number | undefined,
    zipcodes: ZipCodeDTO[],
  ): void {
    if (zipCodeId) {
      const matchingZipcode = zipcodes.find((z) => z.id === zipCodeId);
      if (matchingZipcode) {
        this.companyForm
          .get(controlName)
          ?.setValue(matchingZipcode.id, { emitEvent: false });
      }
    }
  }

  private handleZipcodesResult(zipcodes: ZipCodeDTO[]): void {
    if (zipcodes.length === 0) {
      this.notification.warning(
        COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
      );
    }
    this.cdr.detectChanges();
  }

  saveCompanyDetails(): void {
    if (!this.companyForm.valid) {
      this.handleInvalidForm();
      return;
    }

    if (!this.company?.id) {
      this.notification.error(
        COMMON_STRINGS.warningMessages.companyIdNotAvailable,
      );
      return;
    }

    const tempCompanyRequest = this.buildCompanyRequest();
    const tempCompany = this.buildTempCompany(tempCompanyRequest);
    this.updateCompanyDetails(tempCompanyRequest, tempCompany);
  }

  cancelEdit(): void {
    this.resetFormState();
    this.formCancelled.emit();
  }

  private resetFormState(): void {
    this.companyStateService.clearTempCompanyData();
    this.companyForm.reset();
    this.isSameAsCompanyDetails = false;
    this.isBillingDetailsAccordionOpen = false;
  }

  private handleInvalidForm(): void {
    this.companyValidationService.handleInvalidForm(this.companyForm);
    this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
  }

  private buildCompanyRequest(): CompanyRequestDTO {
    const formValue = this.companyForm.getRawValue();
    return this.companyFormService.buildCompanyRequest(
      formValue,
      this.company,
      this.isSameAsCompanyDetails,
    );
  }

  private buildTempCompany(
    tempCompanyRequest: CompanyRequestDTO,
  ): ICompanyFields {
    return this.companyFormService.buildTempCompany(
      tempCompanyRequest,
      this.company,
      this.states,
      this.postalCode,
      this.billingZipcode,
      this.companyStateService.getTempCompanyData(),
    );
  }

  private updateCompanyDetails(
    tempCompanyRequest: CompanyRequestDTO,
    tempCompany: ICompanyFields,
  ): void {
    this.isLoading = true;
    this.subscriptions.add(
      this.registerService
        .updateCompany(this.company!.id!, tempCompanyRequest)
        .subscribe({
          next: (response: ApiResponseCompanyDTO) =>
            this.handleUpdateSuccess(response, tempCompany),
          error: (error) => this.handleUpdateError(error),
        }),
    );
  }

  private handleUpdateSuccess(
    response: ApiResponseCompanyDTO,
    tempCompany: ICompanyFields,
  ): void {
    this.isLoading = false;
    if (response.success && response.data) {
      this.processSuccessfulUpdate(response, tempCompany);
    } else {
      this.handleUpdateFailure(response.message);
    }
  }

  private processSuccessfulUpdate(
    response: ApiResponseCompanyDTO,
    tempCompany: ICompanyFields,
  ): void {
    this.company = { ...tempCompany, ...response.data };
    this.companyStateService.setCompanyData(this.company);
    this.companyStateService.clearTempCompanyData();
    this.resetUpdateState();
    this.notification.success(
      COMMON_STRINGS.successMessages.companyUpdateSuccess,
    );
    this.companySaved.emit(response.data!);
  }

  private resetUpdateState(): void {
    this.isBillingDetailsAccordionOpen = false;
    this.isSameAsCompanyDetails = false;
  }

  private handleUpdateFailure(message?: string): void {
    this.notification.error(
      COMMON_STRINGS.warningMessages.companyUpdateFailure.replace(
        '${errorMessage}',
        message || '',
      ),
    );
  }

  private handleUpdateError(error: HttpErrorResponse): void {
    this.isLoading = false;
    const errorMessage = error.error?.message || error.message;
    this.notification.error(
      COMMON_STRINGS.warningMessages.companyUpdateFailure.replace(
        '${errorMessage}',
        errorMessage,
      ),
    );
  }

  onSameAsCompanyDetailsChange(checked: boolean): void {
    this.isSameAsCompanyDetails = checked;

    if (checked) {
      this.copyPrimaryToBillingAddress();
      this.disableBillingFields();
    } else {
      this.enableBillingFields();
    }

    this.companyForm.markAsDirty();
    this.cdr.detectChanges();
  }

  private copyPrimaryToBillingAddress(): void {
    const primaryValues = this.companyFormService.copyPrimaryToBillingAddress(
      this.companyForm,
    );
    this.companyForm.patchValue(primaryValues);
    this.billingZipcode = this.postalCode;
  }

  private disableBillingFields(): void {
    this.companyFormService.manageBillingFields(this.companyForm, true);
  }

  private enableBillingFields(): void {
    this.companyFormService.manageBillingFields(this.companyForm, false);
  }

  getCompanyErrorTip(controlName: string): string | undefined {
    return this.companyValidationService.getCompanyErrorTip(
      this.companyForm,
      controlName,
    );
  }

  isCompanyDataUnchanged(
    original: ICompanyFields,
    updated: ICompanyFields,
  ): boolean {
    return this.companyValidationService.isCompanyDataUnchanged(
      original,
      updated,
    );
  }

  toggleBillingDetailsAccordion(): void {
    this.isBillingDetailsAccordionOpen = !this.isBillingDetailsAccordionOpen;
  }
}
