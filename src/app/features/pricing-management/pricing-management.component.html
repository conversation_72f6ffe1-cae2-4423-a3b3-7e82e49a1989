<div class="pricing-management-container">
  <div
    style="
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    "
  >
    <div class="search-container">
      <nz-input-group [nzSuffix]="suffixIconSearch">
        <input
          class="search-input"
          type="text"
          nz-input
          placeholder="Search document here"
          [(ngModel)]="searchText"
          (ngModelChange)="onSearch()"
        />
      </nz-input-group>
      <ng-template #suffixIconSearch>
        <nz-icon nzType="search" style="color: #666; font-size: 16px" />
      </ng-template>
    </div>
    <button
      class="add-document-button"
      nz-button
      nzType="primary"
      (click)="toggleAddDocumentDrawer()"
    >
      Add document
    </button>
  </div>

  <app-data-grid
    [tableColumns]="pricingManagementTableColumns"
    [tableData]="filteredTableData"
    [loading]="isLoading"
    [showPagination]="true"
    (tableDataClick)="onTableDataClick($event)"
  >
  </app-data-grid>

  <nz-drawer
    [nzClosable]="false"
    class="drawer-header"
    [nzVisible]="isAddDocumentDrawerOpen"
    [nzTitle]="isEditMode ? 'Edit Document' : 'Add Document'"
    nzPlacement="right"
    (nzOnClose)="toggleAddDocumentDrawer()"
  >
    <ng-container *nzDrawerContent>
      <form
        *ngIf="addDocumentForm"
        nz-form
        nzLayout="vertical"
        [formGroup]="addDocumentForm"
      >
        <div nz-row nzGutter="16">
          <div nz-col [nzMd]="24">
            <nz-form-item class="mb-3 nzdrawer-form-item">
              <nz-form-label class="form-label" [nzRequired]="true"
                >Document Name</nz-form-label
              >
              <nz-form-control
                [nzErrorTip]="
                  pricingFormHelper.getErrorTip(addDocumentForm, 'name')
                "
              >
                <input
                  class="nz-drawer-form-field"
                  nz-input
                  placeholder="Enter document name"
                  formControlName="name"
                />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="mb-3 nzdrawer-form-item">
              <nz-form-label class="form-label" [nzRequired]="true"
                >Description</nz-form-label
              >
              <nz-form-control
                [nzErrorTip]="
                  pricingFormHelper.getErrorTip(addDocumentForm, 'description')
                "
              >
                <input
                  class="nz-drawer-form-field"
                  nz-input
                  placeholder="Enter description"
                  formControlName="description"
                />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="mb-3 nzdrawer-form-item">
              <nz-form-label class="form-label" [nzRequired]="true"
                >Product Code</nz-form-label
              >
              <nz-form-control
                [nzErrorTip]="
                  pricingFormHelper.getErrorTip(addDocumentForm, 'productCode')
                "
              >
                <input
                  class="nz-drawer-form-field"
                  nz-input
                  placeholder="Enter product code"
                  formControlName="productCode"
                />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="mb-3 nzdrawer-form-item">
              <nz-form-label class="form-label" [nzRequired]="true"
                >Cost (Ex. GST)</nz-form-label
              >
              <nz-form-control
                [nzErrorTip]="
                  pricingFormHelper.getErrorTip(addDocumentForm, 'basePrice')
                "
              >
                <input
                  class="nz-drawer-form-field"
                  nz-input
                  placeholder="Enter platform price (Ex. GST)"
                  type="number"
                  formControlName="basePrice"
                />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="mb-3 nzdrawer-form-item">
              <nz-form-label class="form-label">Updated Price</nz-form-label>
              <nz-form-control
                [nzErrorTip]="
                  pricingFormHelper.getErrorTip(
                    addDocumentForm,
                    'effectiveBasePrice'
                  )
                "
              >
                <input
                  class="nz-drawer-form-field"
                  nz-input
                  placeholder="Enter marked up price"
                  type="number"
                  formControlName="effectiveBasePrice"
                />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="mb-3 nzdrawer-form-item">
              <nz-form-label class="form-label" [nzRequired]="true"
                >Effective from</nz-form-label
              >
              <nz-form-control
                [nzErrorTip]="
                  pricingFormHelper.getErrorTip(
                    addDocumentForm,
                    'effectiveDate'
                  )
                "
              >
                <nz-date-picker
                  class="nz-drawer-form-field"
                  formControlName="effectiveDate"
                  nzPlaceHolder="Select effective date"
                >
                </nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <div
          class="user-edit-form-buttons mt-6"
        >
          <button
            nz-button
            nzType="primary"
            class="save-edit-button"
            type="button"
            (click)="isEditMode ? updateDocument() : addDocument()"
            [disabled]="
              isEditMode
                ? !isUpdateButtonEnabled()
                : !pricingFormHelper.isAddedDocumentFormValid(
                    addDocumentForm,
                    isEditMode,
                    selectedDocumentToEdit,
                    pricingManagementTableData
                  )
            "
          >
            {{ isEditMode ? 'Save Document' : 'Add Document' }}
          </button>
          <button
            nz-button
            nzType="primary"
            nzGhost
            (click)="toggleAddDocumentDrawer()"
          >
            Cancel
          </button>
        </div>
      </form>
    </ng-container>
  </nz-drawer>

  <ng-template #confirmModal>
    <form nz-form nzLayout="vertical" [formGroup]="priceModalForm">
      <nz-form-item>
        <nz-form-label nzRequired>Effective Date</nz-form-label>
        <nz-form-control
          [nzErrorTip]="
            pricingFormHelper.getErrorTip(priceModalForm, 'effectiveDate')
          "
        >
          <nz-date-picker
            class="nz-drawer-form-field"
            formControlName="effectiveDate"
            nzPlaceHolder="Select effective date"
          ></nz-date-picker>
        </nz-form-control>
      </nz-form-item>
      <div style="display: flex; justify-content: flex-start; gap: 8px">
        <button
          nz-button
          nzType="primary"
          (click)="handlePriceModalSave()"
          [disabled]="!priceModalForm.valid"
        >
          Save
        </button>
        <button nz-button (click)="handlePriceModalCancel()">Cancel</button>
      </div>
    </form>
  </ng-template>
</div>
