import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { cloneDeep } from 'lodash';

import {
  ApiResponseDocumentPriceResponseDTO,
  ApiResponsePageDocumentPriceResponseDTO,
  DocumentPriceRequestDTO,
} from '../../api-client';
import { COMMON_STRINGS } from '../../core/constants/common';
import { DataGridComponent } from '../data-grid/data-grid.component';
import { IPricingFields } from '../../core/interface/pricing-fields';
import { ITableDataClickOutput } from '../../core/interface/table';
import { NotificationService } from '../../core/services/notification.service';
import { PricingService } from '../../core/services/pricing.service';
import { PRICING_MANAGEMENT_GLOBAL_LEVEL_TABLE_COLUMNS } from '../../core/tableColumns/pricing.column';
import { PricingFormHelperComponent } from '../../core/components/pricing-form';

@Component({
  selector: 'app-pricing-management',
  imports: [
    DataGridComponent,
    NzInputModule,
    NzIconModule,
    FormsModule,
    NzButtonModule,
    NzDrawerModule,
    NzFormModule,
    CommonModule,
    ReactiveFormsModule,
    NzDatePickerModule,
    NzModalModule,
  ],
  templateUrl: './pricing-management.component.html',
  styleUrls: ['./pricing-management.component.scss', '../../../styles.scss'],
})
export class PricingManagementComponent implements OnInit {
  @ViewChild('confirmModal') confirmModal!: TemplateRef<unknown>;

  pricingManagementTableColumns = PRICING_MANAGEMENT_GLOBAL_LEVEL_TABLE_COLUMNS;
  pricingManagementTableData: IPricingFields[] = [];
  filteredTableData: IPricingFields[] = [];
  isLoading = false;
  searchText = '';
  isAddDocumentDrawerOpen = false;
  addDocumentForm: FormGroup;
  isEditMode = false;
  selectedDocumentToEdit: IPricingFields | null = null;
  originalFormValues: IPricingFields | null = null;
  priceModalForm: FormGroup;
  isPriceModalVisible = false;
  selectedRowData: IPricingFields | null = null;

  constructor(
    private fb: FormBuilder,
    private notification: NotificationService,
    private pricingService: PricingService,
    private modalService: NzModalService,
    public pricingFormHelper: PricingFormHelperComponent,
  ) {
    this.addDocumentForm = this.pricingFormHelper.initializeAddDocumentForm();
    this.priceModalForm = this.fb.group({
      effectiveDate: [''],
    });
  }

  ngOnInit(): void {
    this.filteredTableData = [...this.pricingManagementTableData];
    this.addDocumentForm.valueChanges.subscribe(() => {
      this.pricingFormHelper.updateProductPriceIncGst(this.addDocumentForm);
      this.pricingFormHelper.checkFormChanges(
        this.addDocumentForm,
        this.isEditMode,
        this.originalFormValues,
      );
      if (this.addDocumentForm.get('effectiveDate')?.dirty) {
        this.addDocumentForm.markAsDirty();
      }
    });
    this.fetchDocuments();
  }

  onTableDataClick(data: ITableDataClickOutput<IPricingFields>): void {
    const { actionField, rowData } = data;
    switch (actionField) {
      case 'edit':
        this.onEditClick(rowData);
        break;
      case 'delete':
        this.onClickDelete(rowData);
        break;
      case 'save':
        this.openPriceModal(rowData);
        break;
    }
  }

  openPriceModal(rowData: IPricingFields): void {
    this.selectedRowData = rowData;
    this.priceModalForm.patchValue({
      effectiveDate: rowData.effectiveDate
        ? new Date(rowData.effectiveDate)
        : null,
    });
    this.isPriceModalVisible = true;
    this.modalService.create({
      nzTitle: 'Update Price',
      nzContent: this.confirmModal,
      nzFooter: null,
      nzOnCancel: () => this.handlePriceModalCancel(),
    });
  }

  handlePriceModalCancel(): void {
    this.isPriceModalVisible = false;
    this.priceModalForm.reset();
    this.selectedRowData = null;
    this.modalService.closeAll();
  }

  handlePriceModalSave(): void {
    if (!this.selectedRowData || !this.selectedRowData.id) {
      this.notification.error(COMMON_STRINGS.warningMessages.noDocSelected);
      return;
    }
    const formValues = this.priceModalForm.getRawValue();
    const updatedRequestDTO: DocumentPriceRequestDTO = {
      name: this.selectedRowData.name?.trim() || '',
      productCode: this.selectedRowData.productCode?.trim() || '',
      description: this.selectedRowData.description?.trim() || '',
      basePrice: this.selectedRowData.basePrice || 0,
      effectiveBasePrice: this.selectedRowData.effectiveBasePrice,
      effectivePriceGst: parseFloat(formValues.effectivePriceGst) || undefined,
      effectiveDate: formValues.effectiveDate
        ? new Date(formValues.effectiveDate).toISOString()
        : new Date().toISOString(),
    };

    this.isLoading = true;
    this.pricingService
      .updateDocumentPrice(this.selectedRowData.id, updatedRequestDTO)
      .subscribe({
        next: (response: ApiResponseDocumentPriceResponseDTO) => {
          if (response && response.data) {
            const updatedDoc: IPricingFields = {
              ...response.data,
              productPriceIncGst:
                (response.data.effectiveBasePrice ??
                  response.data.basePrice ??
                  0) + (response.data.effectivePriceGst ?? 0),
            };

            const index = this.pricingManagementTableData.findIndex(
              (doc) => doc.id === this.selectedRowData!.id,
            );
            if (index !== -1) {
              this.pricingManagementTableData[index] = updatedDoc;
              this.filteredTableData = [...this.pricingManagementTableData];
            }

            this.notification.success(
              COMMON_STRINGS.successMessages.documentPriceUpdateSuccess.replace(
                '${documentName}',
                updatedDoc.name || '',
              ),
            );
            this.handlePriceModalCancel();
          } else {
            this.notification.error(
              COMMON_STRINGS.errorMessages.failedToUpdateDocumentPrice,
            );
          }
          this.isLoading = false;
        },
        error: () => {
          this.notification.error(
            COMMON_STRINGS.errorMessages.failedToUpdateDocumentPrice,
          );
          this.isLoading = false;
        },
      });
  }

  fetchDocuments() {
    this.isLoading = true;
    this.pricingService.getAllDocumentPrices().subscribe({
      next: (response: ApiResponsePageDocumentPriceResponseDTO) => {
        if (response && response.data && response.data.content) {
          this.pricingManagementTableData = response.data.content.map(
            (item) => ({
              ...item,
              productPriceIncGst: item.effectiveBasePrice || 0,
            }),
          );
          this.filteredTableData = [...this.pricingManagementTableData];
        } else {
          this.pricingManagementTableData = [];
          this.filteredTableData = [];
          this.notification.warning(COMMON_STRINGS.warningMessages.noDataFound);
        }
        this.isLoading = false;
      },
      error: () => {
        this.notification.error(
          COMMON_STRINGS.errorMessages.failedToFetchDocuments,
        );
        this.isLoading = false;
      },
    });
  }

  onSearch() {
    const search = this.searchText.trim().toLowerCase();
    this.filteredTableData = this.pricingManagementTableData.filter(
      (item) =>
        item.name?.toLowerCase().includes(search) ||
        item.productCode?.toLowerCase().includes(search),
    );
  }

  onEditClick(rowData: IPricingFields): void {
    if (!rowData || !rowData.id || !rowData.name) {
      this.notification.error(COMMON_STRINGS.warningMessages.noDocSelected);
      return;
    }

    this.isEditMode = true;
    this.selectedDocumentToEdit = rowData;
    this.originalFormValues = cloneDeep(rowData);

    const formData: Partial<IPricingFields> = {
      ...rowData,
      effectiveDate: rowData.effectiveDate,
      productPriceIncGst:
        rowData.productPriceIncGst ?? rowData.effectiveBasePrice ?? 0,
    };

    this.addDocumentForm.patchValue(formData);
    this.pricingFormHelper.setFormValidators(this.addDocumentForm);
    this.addDocumentForm.markAsPristine();
    this.toggleAddDocumentDrawer();
  }

  onClickDelete(rowData: IPricingFields) {
    console.log('delete', rowData);
  }

  toggleAddDocumentDrawer(): void {
    this.isAddDocumentDrawerOpen = !this.isAddDocumentDrawerOpen;
    if (this.isAddDocumentDrawerOpen) {
      if (!this.isEditMode) {
        this.addDocumentForm.reset();
        this.addDocumentForm.patchValue({
          effectiveDate: new Date(),
        });
      }
      this.pricingFormHelper.setFormValidators(this.addDocumentForm);
    } else {
      this.pricingFormHelper.clearFormValidators(this.addDocumentForm);
      this.addDocumentForm.reset();
      this.isEditMode = false;
      this.selectedDocumentToEdit = null;
      this.originalFormValues = null;
    }
  }

  addDocument() {
    if (
      !this.pricingFormHelper.isAddedDocumentFormValid(
        this.addDocumentForm,
        this.isEditMode,
        this.selectedDocumentToEdit,
        this.pricingManagementTableData,
      )
    ) {
      this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
      this.addDocumentForm.markAllAsTouched();
      return;
    }

    const formValues = this.addDocumentForm.getRawValue();
    const requestDTO: DocumentPriceRequestDTO = {
      name: formValues.name.trim(),
      productCode: formValues.productCode?.trim(),
      description: formValues.description.trim(),
      basePrice: parseFloat(formValues.basePrice),
      effectiveBasePrice: formValues.effectiveBasePrice
        ? parseFloat(formValues.effectiveBasePrice)
        : undefined,
      effectivePriceGst: formValues.effectivePriceGst
        ? parseFloat(formValues.effectivePriceGst)
        : undefined,
      effectiveDate: formValues.effectiveDate
        ? new Date(formValues.effectiveDate).toISOString()
        : new Date().toISOString(),
    };

    this.isLoading = true;
    this.pricingService.createNewDocumentPrice(requestDTO).subscribe({
      next: (response: ApiResponseDocumentPriceResponseDTO) => {
        if (response && response.data) {
          const newDoc: IPricingFields = {
            ...response.data,
            productPriceIncGst:
              (response.data.effectiveBasePrice ??
                response.data.basePrice ??
                0) + (response.data.effectivePriceGst ?? 0),
          };

          this.pricingManagementTableData.unshift(newDoc);
          this.filteredTableData = [...this.pricingManagementTableData];
          this.notification.success(
            COMMON_STRINGS.successMessages.documentAddedSuccess.replace(
              '${documentName}',
              newDoc.name || '',
            ),
          );
          this.toggleAddDocumentDrawer();
        } else {
          this.notification.error(
            COMMON_STRINGS.errorMessages.failedToAddDocument,
          );
        }
        this.isLoading = false;
      },
      error: () => {
        this.notification.error(
          COMMON_STRINGS.errorMessages.failedToAddDocument,
        );
        this.isLoading = false;
      },
    });
  }

  isUpdateButtonEnabled(): boolean {
    return (
      this.isEditMode &&
      this.addDocumentForm.valid &&
      this.addDocumentForm.dirty
    );
  }

  updateDocument(): void {
    if (
      !this.isEditMode ||
      !this.selectedDocumentToEdit ||
      !this.selectedDocumentToEdit.id
    ) {
      this.notification.error(COMMON_STRINGS.warningMessages.noDocSelected);
      return;
    }

    const formValues = this.addDocumentForm.getRawValue();
    const requiredFields = ['name', 'description', 'basePrice'];
    const hasMissingRequired = requiredFields.some(
      (field) => !formValues[field]?.toString().trim(),
    );

    if (hasMissingRequired) {
      this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
      this.addDocumentForm.markAllAsTouched();
      return;
    }

    if (
      formValues.name &&
      formValues.name.trim().toLowerCase() !==
        this.selectedDocumentToEdit.name?.toLowerCase() &&
      !this.pricingFormHelper.isDocumentNameUnique(
        formValues.name.trim(),
        this.isEditMode,
        this.selectedDocumentToEdit,
        this.pricingManagementTableData,
      )
    ) {
      this.notification.error('Document name must be unique.');
      return;
    }

    const updatedRequestDTO: DocumentPriceRequestDTO = {
      name: formValues.name.trim(),
      productCode: formValues.productCode?.trim() || '',
      description: formValues.description.trim(),
      basePrice: parseFloat(formValues.basePrice),
      effectiveBasePrice: formValues.effectiveBasePrice
        ? parseFloat(formValues.effectiveBasePrice)
        : undefined,
      effectivePriceGst: formValues.effectivePriceGst
        ? parseFloat(formValues.effectivePriceGst)
        : undefined,
      effectiveDate: formValues.effectiveDate
        ? new Date(formValues.effectiveDate).toISOString()
        : new Date().toISOString(),
    };

    this.isLoading = true;
    const docId = this.selectedDocumentToEdit.id;

    this.pricingService
      .updateDocumentPrice(docId, updatedRequestDTO)
      .subscribe({
        next: (response: ApiResponseDocumentPriceResponseDTO) => {
          if (response && response.data) {
            const updatedDoc: IPricingFields = {
              ...response.data,
              productPriceIncGst:
                (response.data.effectiveBasePrice ??
                  response.data.basePrice ??
                  0) + (response.data.effectivePriceGst ?? 0),
            };

            const index = this.pricingManagementTableData.findIndex(
              (doc) => doc.id === docId,
            );
            if (index !== -1) {
              this.pricingManagementTableData[index] = updatedDoc;
              this.filteredTableData = [...this.pricingManagementTableData];
            }

            this.notification.success(
              COMMON_STRINGS.successMessages.documentUpdateSuccess.replace(
                '${documentName}',
                updatedDoc.name || '',
              ),
            );
            this.toggleAddDocumentDrawer();
          } else {
            this.notification.error(
              COMMON_STRINGS.errorMessages.failedToUpdateDocumentPrice,
            );
          }
          this.isLoading = false;
        },
        error: () => {
          this.notification.error(
            COMMON_STRINGS.errorMessages.failedToUpdateDocumentPrice,
          );
          this.isLoading = false;
        },
      });
  }
}
