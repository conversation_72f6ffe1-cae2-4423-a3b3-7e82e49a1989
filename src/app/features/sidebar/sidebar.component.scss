/* Sidebar container */
.sidebar {
  width: var(--sidebar-expanded-width);
  height: 100vh;
  background-color: var(--sidebar-background);
  display: flex;
  flex-direction: column;
  box-shadow: var(--sidebar-box-shadow);
  position: fixed;
  left: 0%;
  right: 0%;
  top: 0%;
  bottom: 0%;
  z-index: 1000;
  transition: width 0.3s ease;
  * {
    overflow: hidden;
  }
}

.logo-container {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-bottom: var(--logo-container-border-bottom);
  position: relative;
}

.logo-container .logo {
  height: 32px;
  margin-right: 10px;
  transition: margin 0.3s ease;
}

.logo-container .logo-text {
  font-size: 23px;
  font-weight: 500;
  color: var(--logo-text-color);
  transition: opacity 0.2s ease;
}

/* Search box */
.search-container {
  padding: 16px;
  transition: padding 0.3s ease;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 12px;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  cursor: pointer;
}

.search-icon {
  color: #2169ac;
  margin-right: 8px;
}

.search-input {
  border: none;
  outline: none;
  width: 100%;
  font-size: 14px;
  transition: opacity 0.2s ease;
}

/* Menu container */
.menu-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.footer-container {
  flex-shrink: 0;
  overflow-y: auto;
  padding: 8px 0;
  cursor: pointer;
}

.menu-list,
.footer-menu {
  list-style: none;
  padding: 7px;
  margin: 0;
}

.menu-item,
.footer-item {
  padding: 6px 12px;
  cursor: pointer;
  transition: background-color 0.3s;
  margin: 2px 0;
  border-radius: 12px;
}

.menu-item:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

.menu-item.active {
  background-color: var(--secondary-application-color);
  font-weight: 500;
}

.menu-item-content,
.footer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 30px;
}

.menu-item-left {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--sidebar-menu-text-color);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.menu-item-left i,
.menu-item-left fa-icon {
  margin-right: 16px;
  width: 16px;
  text-align: center;
  color: var(--sidebar-menu-icon-color, #1890ff);
  transition: margin 0.3s ease;
  margin-top: 3px;
}

.arrow-icon {
  font-size: 12px;
  color: #2169ac;
  margin-left: auto;
}

.footer-item:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

.footer-item.active {
  background-color: rgba(24, 144, 255, 0.2);
  font-weight: 500;
}

.footer-item fa-icon {
  margin-right: 16px;
  width: 16px;
  text-align: center;
  color: #084986;
}

/* User profile */
.user-profile {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 16px;
  transition: padding 0.3s ease;
  height: 47px;
  background: var(--secondary-application-color);
  border-radius: 12px;
  margin-left: 10px;
  margin-right: 10px;
}

.avatar {
  width: 32px;
  height: 32px;
  background-color: #aad3ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  font-size: 19px;
  font-weight: 500;
  color: #ffffff;
  flex-shrink: 0;
  transition: margin 0.3s ease;
}

.user-info {
  display: flex;
  flex-direction: column;
  transition: opacity 0.2s ease;
}

.username {
  font-size: 16px;
  font-weight: 500;
  color: #000;
}

.email {
  font-size: 12px;
  color: #595959;
}

.username,
.email {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Collapsed styles */
.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar.collapsed .logo-container {
  padding: 16px 6px;
  justify-content: center;
}

.sidebar.collapsed .logo {
  margin-right: 0;
}

.sidebar .logo-text {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.sidebar.collapsed .logo-text,
.sidebar.collapsed .search-input,
.sidebar.collapsed .menu-item-left span,
.sidebar.collapsed .arrow-icon,
.sidebar.collapsed .user-info {
  opacity: 0;
  width: 0;
  display: none;
  overflow: hidden;
}

.sidebar.collapsed .search-icon {
  margin-right: 0;
}

.sidebar.collapsed .search-container {
  padding: 16px 8px;
}

.sidebar.collapsed .menu-item,
.sidebar.collapsed .footer-item {
  padding: 12px 8px;
  margin: 8px 6px;
  justify-content: center;
  align-items: center;
}

.sidebar.collapsed .menu-item-left i {
  margin-right: 0;
}

.sidebar.collapsed .avatar {
  margin-right: 0;
}

.sidebar.collapsed .user-profile {
  padding: 12px 8px;
  justify-content: center;
}

/* Collapse toggle button */
.collapse-button {
  background-color: var(--primary-button-color);
  border: none;
  cursor: pointer;
  font-size: 16px;
  height: 4rem;
  width: 1.25rem;
  top: 45vh;
  left: calc(var(--sidebar-expanded-width) + 0rem);
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  clip-path: polygon(0 0, 100% 20%, 100% 80%, 0 100%);
  z-index: 1000;
  transition: left 0.3s ease;
}

.collapse-button.collapsed {
  left: calc(var(--sidebar-collapsed-width) + 0rem);
}
.fa {
  color: #ffffff;
  font-size: 12px;
}
/* Divider */
.sidebar-divider {
  height: 1px;
  background-color: var(--logo-container-border-bottom);
  opacity: 1;
  margin: 10px 15px;
  width: calc(100% - 30px);
}

/* Tooltip for collapsed menu items */
.sidebar.collapsed .menu-item,
.footer-item {
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 6px 8px;
  margin: 2px 4px;
}

@media (max-width: 500px) {
  .sidebar.collapsed .menu-item,
  .footer-item {
    padding: 0;
    margin: 0;
  }
  .sidebar {
    width: var(--sidebar-collapsed-width);
  }

  .sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
  }

  .sidebar.collapsed .logo-container {
    padding: 16px 0;
  }

  .sidebar.collapsed .logo {
    display: none;
  }

  .sidebar.collapsed .logo-text {
    display: none;
  }
}

.sidebar.collapsed .menu-item-content {
  display: flex;
  justify-content: center;
}

.sidebar.collapsed .menu-item:hover::after {
  content: attr(data-title);
  position: absolute;
  left: 70px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1001;
}

.logo-container.collapsed {
  justify-content: center;
  padding: 16px 0;
}

.logo {
  height: 32px;
  margin-right: 10px;
  transition: margin 0.3s ease;
}

.logo-container.collapsed .logo {
  margin-right: 0;
}

/* Collapse toggle button */
.collapse-toggle {
position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--sidebar-background);
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logo-collapsed {
  height: 32px;
  margin: 0 auto;
  display: block;
}
