export const COMMON_STRINGS = {
  register: {
    userSuccess: 'User created successfully',
    companySuccess: 'Company created successfully',
    userRegistrationFailure: 'User registration failed',
    companyRegistrationFailure: 'Company registration failed',
  },
  confirmMessages: {
    deleteUserConfirmation: 'Are you sure you want to delete ${finalUserName}?',
    deactivateCompanyConfirmation:
      'Are you sure you want to deactivate ${companyName}? This will also deactivate all associated users and may take a moment to complete.',
    toggleUserStatusConfirmation:
      'Are you sure you want to ${actionText} ${userName}?',
  },
  errorMessages: {
    failedToFetchPropertyDetails: 'Failed to fetch property details',
    failedToFetchProductDetails: 'Failed to fetch product details',
    failedToFetchCartItems: 'Failed to fetch cart items',
    failedToCreateOrder: 'Failed to create order',
    yourCartIsEmpty: 'Your cart is empty',
    failedToFetchWalletBalance: 'Failed to fetch wallet balance',
    failedToRechargeWallet: 'Failed to recharge wallet',
    handleUserError: 'Failed to get user information. Please log in again.',
    failedToFetchTransactions: 'Failed to fetch transactions',
    insufficientWalletBalance:
      'Insufficient wallet balance. Please Recharge your wallet.',
    displayRolesFailed: 'Failed to load roles',
    failedToFetchStates: 'Failed to fetch states',
    failedToFetchZipcodes: 'Failed to fetch zip codes',
    failedToUpdateUser: 'Failed to update user',
    noNameChange: 'Name is required',
    noContactNumberChange: 'Contact number is required',
    invalidContactNumber: 'Invalid contact number format',
    invalidEmail: 'Invalid email format',
    noEmailChange: 'Email is required',
    noAddressChange: 'Address is required',
    failedToRemoveFromCart: 'Failed to remove item from cart',
    failedToFetchDocuments: 'Failed to fetch documents',
    failedToSendPasswordUpdateEmail:
      'Failed to send password update email, please try again',
    noEmailAvailable: 'No email address available for password reset.',
    failedToFetchPresignedUrl: 'Failed to fetch file URL',
    invalidFileType: 'Only PNG, JPEG, and GIF files are allowed',
    fileExceedsSizeLimit: 'File size exceeds 5MB limit',
    failedToFetchCompanies: 'Failed to fetch companies',
    companyIdNotFound: 'Company ID not found',
    companyNotFoundInData: 'Company not found in data',
    failedToFetchUsers: 'Failed to fetch users',
    userIdNotFound: 'User ID not found',
    failedToToggleCompanyKeycloak:
      'Failed to ${actionText} company: Some users could not be updated in the authentication system. Please try again.',
    failedToToggleCompany: 'Failed to ${actionText} company: ${errorMessage}',
    failedToToggleCompanyUserPermissions:
      'Failed to ${actionText} company: Unable to update user access permissions.Please try again in a few moments.',
    failedToToggleCompanyException:
      'Failed to ${actionText} company: ${errorMessage}',
    companyOperationFailed: 'Company operation failed',
    failedToToggleUser: 'Failed to ${actionText} user: ${errorMessage}',
    failedToToggleUserNetwork: 'Failed to ${actionText} user: ${errorMessage}',
    failedToUpdateCompany: '${errorMessage}',
    companyAddressRequired: 'Company addresses are required to update a user.',
    companyIdIsRequired: 'Company ID is required to add a user.',
    failedToAddUser: 'Failed to add user: Invalid user data.',
    documentNameExists: 'Document name already exists',
    failedToUpdateDocument: 'Failed to update document',
    failedToFetchData: 'Failed to fetch data',
    failedToUpdateDocumentPrice: 'Failed to update document price',
    failedToAddDocument: 'Failed to add document',
  },
  warningMessages: {
    addressSearchWarning: 'Please enter an address to search.',
    planSearchWarning: 'Please enter a valid plan number to search.',
    volumeSearchWarning: 'Please enter a valid volume folio to search.',
    deleteUserWarning: 'User "${finalUserName}" not found.',
    deleteCompanyWarning: 'Company "${name}" not found.',
    addAnotherUser: 'A user with this email already exists.',
    addToCartWarning: 'Please select a valid document to add to cart.',
    enterAllFields: 'Please fill all required fields',
    companyCreationFailure:
      'Company created but failed to create users: ${this.errorMessage}',
    companyIdNotFound: 'Company created but no company ID received',
    companyStatusUpdateFailure:
      'Failed to update company status: ${errorMessage}',
    missingCompanyId: 'Cannot toggle company status: Company ID is missing',
    itemPresentInCartMessage:
      'This item is already in your cart. You can proceed to checkout.',
    userCreationFailed: 'Failed to create user. Please try again.',
    enterValidPhoneNumber: 'Please enter a valid phone number',
    enterValidEmail: 'Please enter a valid email address',
    userWarning:
      'Some users have a null or different company ID. Please check the API filtering.',
    displayUsersFailed: 'Failed to load users',
    companyIdNotAvailable: 'No company ID available',
    companyUpdateFailure: 'Failed to update company: ${errorMessage}',
    userUpdateFailure: 'Failed to update user: ${errorMessage}',
    noChangesFound: 'No changes found, please update to save.',
    cartEmptyMessage:
      'Your cart is empty. Please add items to the cart before creating an order.',
    failedToOpenCompanyView: 'Failed to open company view',
    editUser: 'Invalid user',
    failedToFetchDocuments: 'Failed to fetch documents',
    selectValidState: 'Please select a valid state',
    noMatchingZipcodeFound: 'No matching zip code found',
    emailAlreadyExists: 'Email already exists in the list',
    userAlreadyExists: 'User already exists in active users.',
    noDataFound: 'No data found',
    noDocSelected: 'No document selected for update.',
  },
  successMessages: {
    deleteUserSuccess: 'User "${finalUserName}" deleted successfully.',
    deleteCompanySuccess: 'Company "${name}" deleted successfully.',
    addToCartSuccess: 'Item added to cart successfully.',
    removeFromCartSuccess: 'Item removed from cart successfully.',
    companyCreated:
      'Company and "${userResponses.length}" user(s) created successfully!',
    companyStatusUpdated: 'Company ${name} is now ${status}',
    orderCreated: 'Order created successfully with ID: ${response.id}',
    rechargeWalletSuccess: 'Wallet recharged successfully with $${amount}',
    toggleStatusUpdated: 'Company status updated successfully',
    companyUpdateSuccess: 'Company updated successfully',
    emailInvoiceSuccess: 'Invoice email sent successfully',
    downloadInvoiceSuccess: 'Invoice downloaded successfully',
    viewDocumentsSuccess: 'Opening documents viewer',
    userStatusUpdated: '${finalUserName} has been successfully ${action}.',
    userUpdateSuccess: 'User updated successfully',
    openDocumentsSuccess: 'Opening documents in new tab',
    passwordUpdateEmailSentSuccessfully:
      'Password update email sent successfully, please check your email to reset your password.',
    companyToggleSuccess: 'Company ${statusText} successfully',
    companyUpdatedSuccess: 'Company "${companyName}" updated successfully!',
    companyCreatedSuccess:
      'Company "${companyName}" created successfully and added to the top of the list!',
    userAddedSuccess: 'User "${firstName} ${lastName}" added successfully!',
    userToggleSuccess: 'User ${actionText}d successfully',
    userAddedToActiveUsers:
      'User ${newUser.fullName} added successfully to active users.',
    documentUpdateSuccess: 'Document updated successfully',
    documentPriceUpdateSuccess:
      'Special price for document "${documentName}" updated successfully.',
    documentAddedSuccess: 'Document "${documentName}" added successfully.',
  },
  infoMessages: {
    fillAllFields: 'Please fill all mandatory user fields before adding.',
    companyStatusChangeCancelled: 'Company status change cancelled',
    toggleOperationInProgress:
      'Toggle operation already in progress for this company',
    activatingCompany: 'Activating company...',
    deactivatingCompany: 'Deactivating company...',
  },
  loadingMessage: {
    rechargeWallet: 'Processing wallet recharge...',
    createOrder: 'Creating order...',
  },
  tooltipTexts: {},
  dialogConfigurations: {
    buttonLabels: {
      Ok: 'Ok',
      Cancel: 'Cancel',
      Exit: 'Exit',
      Yes: 'Yes',
      No: 'No',
      SaveChanges: 'Save',
      DoNotSave: 'Do Not Save',
      Continue: 'Continue',
      Delete: 'Delete',
      ConfirmDelete: 'Confirm Delete',
      ConfirmDeactivate: 'Confirm Deactivate',
      ConfirmActivate: 'Confirm Activate',
    },
    messages: {},
    title: {
      Arealytics: 'Arealytics',
    },
    buttonClasses: {
      success: 'btn btn-success',
      warn: 'btn btn-warn',
      danger: 'btn btn-danger',
      primary: 'btn btn-primary',
      primaryBlue: 'btn btn-primary-blue',
      info: 'btn btn-info',
    },
  },
  deletion: {
    deleteCompany: 'Company ABN/ACN is missing. Cannot delete.',
    deleteUser: 'User email is missing. Cannot delete.',
  },
};

export const DEFAULT_PAGE_SIZE = 50;

export const DEFAULT_PAGE_OFFSET_OPTIONS = [150, 100, 50, 25, 10];

export const DEFAULT_CURRENCY = 'AUD';

export const DEFAULT_RECHARGE_AMOUNT = 0;

export const PHONE_REGEX =
  /^\+?1?\s*[-.\s]?\(?[2-9][0-8]\d\)?[-.\s]?\d{3}[-.\s]?\d{4}$/;

export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

export const PRESET_AMOUNT = [
  1000, 1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000,
];

export const transactionId = 13245;
export const mediaURL = 's3.amazonaws.com';
