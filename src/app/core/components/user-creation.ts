import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { switchMap, catchError, map } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';
import {
  UserRequestDTO,
  ApiResponseUserResponseDTO,
  CompanyDTO,
  UserResponseDTO,
  AddressRequestDTO,
  AddressResponseDTO,
} from '../../api-client';

import { COMMON_STRINGS } from '../constants/common';
import { NotificationService } from '../services/notification.service';
import { RegisterService } from '../services/register.service';
import { S3Service } from '../services/s3upload.service';

export interface UserFormData {
  firstName: string;
  lastName: string;
  email: string;
  contactNumber: string;
  roleId: number;
  profilePictureUrl?: string | null;
}

export interface UserCreationRequest {
  formData: UserFormData;
  companyId: number;
  company?: CompanyDTO;
  selectedFile?: File | null;
  userType?: 'COMPANY' | 'INDIVIDUAL';
}

export interface UserCreationResult {
  success: boolean;
  userData?: UserResponseDTO;
  error?: string;
}

@Injectable({
  providedIn: 'root',
})
export class UserCreation {
  constructor(
    private registerService: RegisterService,
    private s3Service: S3Service,
    private httpClient: HttpClient,
    private notification: NotificationService,
  ) {}

  /**
   * Create user with optional profile picture upload
   */
  createUserWithProfilePicture(
    request: UserCreationRequest,
  ): Observable<UserCreationResult> {
    const userData = this.buildUserRequest(request);

    if (request.selectedFile) {
      return this.createUserWithFileUpload(userData, request.selectedFile);
    } else {
      return this.createUserWithoutFile(userData);
    }
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/png', 'image/jpeg', 'image/gif'];

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: COMMON_STRINGS.errorMessages.invalidFileType,
      };
    }

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: COMMON_STRINGS.errorMessages.fileExceedsSizeLimit,
      };
    }

    return { isValid: true };
  }

  /**
   * Get content type for file
   */
  getContentType(file: File): string {
    const extension = file.name.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'png':
        return 'image/png';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'gif':
        return 'image/gif';
      default:
        return 'application/octet-stream';
    }
  }

  /**
   * Generate unique filename for upload
   */
  generateFileName(email: string, file: File): string {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    return `${uuidv4()}${email.replace('@', '_')}.${fileExtension}`;
  }

  /**
   * Create user with file upload
   */
  private createUserWithFileUpload(
    userData: UserRequestDTO,
    selectedFile: File,
  ): Observable<UserCreationResult> {
    const fileName = this.generateFileName(userData.email, selectedFile);

    return this.s3Service.getPresignedUrl(userData.email, fileName).pipe(
      switchMap((presignedUrlResponse) => {
        const presignedUrl = presignedUrlResponse.fileUrl;
        if (!presignedUrl) {
          throw new Error('Presigned URL is undefined');
        }

        // Set profile picture URL (without query parameters)
        userData.profilePictureUrl = presignedUrl.split('?')[0];

        return this.registerService.createUser(userData).pipe(
          switchMap((createUserResponse: ApiResponseUserResponseDTO) => {
            const contentType = this.getContentType(selectedFile);
            return this.httpClient
              .put<void>(presignedUrl, selectedFile, {
                headers: { 'Content-Type': contentType },
              })
              .pipe(
                catchError((error: HttpErrorResponse) => {
                  console.error(
                    'PUT request failed:',
                    error.status,
                    error.error,
                  );
                  return throwError(() => error);
                }),
                map(() => createUserResponse),
              );
          }),
        );
      }),
      map((response: ApiResponseUserResponseDTO) => ({
        success: true,
        userData: response.data,
      })),
      catchError((error: HttpErrorResponse) => {
        console.error('Error in API chain:', error.status, error.error);
        const errorMessage =
          error.error?.message ||
          'Failed to add user or upload profile picture';
        this.notification.error(errorMessage);
        return throwError(() => ({
          success: false,
          error: errorMessage,
        }));
      }),
    );
  }

  /**
   * Create user without file upload
   */
  private createUserWithoutFile(
    userData: UserRequestDTO,
  ): Observable<UserCreationResult> {
    return this.registerService.createUser(userData).pipe(
      map((response: ApiResponseUserResponseDTO) => ({
        success: true,
        userData: response.data,
      })),
      catchError((error: HttpErrorResponse) => {
        console.error('Error in createUser API:', error);
        const errorMessage =
          error.error?.message ||
          COMMON_STRINGS.warningMessages.userCreationFailed;
        this.notification.error(errorMessage);
        return throwError(() => ({
          success: false,
          error: errorMessage,
        }));
      }),
    );
  }

  /**
   * Build user request from form data and additional info
   */
  private buildUserRequest(request: UserCreationRequest): UserRequestDTO {
    const { profilePictureUrl, ...formDataWithoutPicture } = request.formData;
    return {
      ...formDataWithoutPicture,
      companyId: request.companyId,
      userType: request.userType || 'COMPANY',
      password: '',
      primaryAddress: this.convertToAddressRequest(
        request.company?.primaryAddress,
      ),
      billingAddress: this.convertToAddressRequest(
        request.company?.billingAddress,
      ),
      profilePictureUrl: profilePictureUrl || undefined,
    };
  }

  /**
   * Convert AddressResponseDTO to AddressRequestDTO
   */
  private convertToAddressRequest(
    address?: AddressResponseDTO,
  ): AddressRequestDTO {
    return {
      addressLine1: address?.addressLine1 || '',
      addressLine2: address?.addressLine2 || '',
      suburb: address?.suburb || '',
      stateId: address?.stateId || 0,
      zipCodeId: address?.zipCodeId || 0,
    };
  }

  /**
   * Handle file selection and validation
   */
  handleFileSelection(
    event: Event,
    onSuccess: (file: File, dataUrl: string) => void,
  ): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];
      const validation = this.validateFile(file);

      if (!validation.isValid) {
        this.notification.error(validation.error!);
        input.value = '';
        return;
      }

      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        const dataUrl = e.target?.result as string;
        onSuccess(file, dataUrl);
      };
      reader.readAsDataURL(file);
    }
  }
}
