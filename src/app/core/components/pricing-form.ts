import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { IPricingFields } from '../../core/interface/pricing-fields';

@Injectable({
  providedIn: 'root',
})
export class PricingFormHelperComponent {
  constructor(private fb: FormBuilder) {}

  initializeAddDocumentForm(): FormGroup {
    return this.fb.group({
      name: ['', Validators.required],
      productCode: [''],
      description: ['', Validators.required],
      basePrice: ['', Validators.required],
      effectiveBasePrice: [''],
      effectivePriceGst: [''],
      productPriceIncGst: [''],
      effectiveDate: [''],
    });
  }

  updateProductPriceIncGst(form: FormGroup): void {
    const formValues = form.getRawValue();
    const basePrice = parseFloat(formValues.basePrice) || 0;
    const effectiveBasePrice = parseFloat(formValues.effectiveBasePrice) || 0;
    const effectivePriceGst = parseFloat(formValues.effectivePriceGst) || 0;

    const priceToUse = effectiveBasePrice || basePrice;
    const productPriceIncGst = priceToUse + effectivePriceGst;

    form.patchValue(
      { productPriceIncGst: productPriceIncGst.toFixed(2) },
      { emitEvent: false },
    );
  }

  setFormValidators(form: FormGroup): void {
    const requiredFields = ['name', 'description', 'productCode', 'basePrice'];

    requiredFields.forEach((field) => {
      const control = form.get(field);
      control?.setValidators(Validators.required);
      control?.updateValueAndValidity();
    });
  }

  clearFormValidators(form: FormGroup): void {
    Object.keys(form.controls).forEach((key) => {
      const control = form.get(key);
      control?.setValidators([]);
      control?.updateValueAndValidity();
    });
  }

  getErrorTip(form: FormGroup, controlName: string): string | undefined {
    const control = form.get(controlName);
    if (control?.touched && control?.hasError('required')) {
      return `${controlName.charAt(0).toUpperCase() + controlName.slice(1)} is required`;
    }
    return undefined;
  }

  isAddedDocumentFormValid(
    form: FormGroup,
    isEditMode: boolean,
    selectedDocument: IPricingFields | null,
    tableData: IPricingFields[],
  ): boolean {
    const name = form.get('name')?.value;
    if (!form.valid) return false;
    return (
      !!name &&
      this.isDocumentNameUnique(name, isEditMode, selectedDocument, tableData)
    );
  }

  isDocumentNameUnique(
    name: string,
    isEditMode: boolean,
    selectedDocument: IPricingFields | null,
    tableData: IPricingFields[],
  ): boolean {
    if (isEditMode && selectedDocument) {
      return !tableData.some(
        (item) =>
          item.name?.toLowerCase() === name.toLowerCase() &&
          item !== selectedDocument,
      );
    }
    return !tableData.some(
      (item) => item.name?.toLowerCase() === name.toLowerCase(),
    );
  }

  hasFormChanged(
    form: FormGroup,
    originalValues: IPricingFields | null,
  ): boolean {
    if (!originalValues) return true;

    const currentValues = form.getRawValue();
    const fieldsToCompare: (keyof IPricingFields)[] = [
      'name',
      'description',
      'productCode',
      'basePrice',
      'effectiveBasePrice',
      'effectivePriceGst',
      'effectiveDate',
    ];

    return fieldsToCompare.some((key) => {
      const current = currentValues[key];
      const original = originalValues[key];

      if (key === 'name' || key === 'description' || key === 'productCode') {
        return (
          (current ?? '').toString().trim() !==
          (original ?? '').toString().trim()
        );
      }

      if (key === 'effectiveDate') {
        const currentDate =
          current &&
          (typeof current === 'string' || typeof current === 'number')
            ? new Date(current)
            : current instanceof Date
              ? current
              : null;

        const originalDate =
          original &&
          (typeof original === 'string' || typeof original === 'number')
            ? new Date(original)
            : original instanceof Date
              ? original
              : null;

        if (!currentDate || isNaN(currentDate.getTime())) {
          return !originalDate || isNaN(originalDate.getTime()) ? false : true;
        }
        if (!originalDate || isNaN(originalDate.getTime())) {
          return true;
        }

        return currentDate.toISOString() !== originalDate.toISOString();
      }

      const currentNum =
        current !== null && current !== undefined ? Number(current) : null;
      const originalNum =
        original !== null && original !== undefined ? Number(original) : null;
      return currentNum !== originalNum;
    });
  }

  checkFormChanges(
    form: FormGroup,
    isEditMode: boolean,
    originalValues: IPricingFields | null,
  ): void {
    if (isEditMode && this.hasFormChanged(form, originalValues)) {
      form.markAsDirty();
    }
  }
}
